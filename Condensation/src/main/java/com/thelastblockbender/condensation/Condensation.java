package com.thelastblockbender.condensation;

import org.bukkit.plugin.Plugin;
import org.bukkit.event.Listener;
import com.projectkorra.projectkorra.ProjectKorra;
import com.projectkorra.projectkorra.configuration.ConfigManager;
import com.projectkorra.projectkorra.ability.util.ComboManager;
import org.bukkit.util.Vector;
import org.bukkit.block.Block;
import com.projectkorra.projectkorra.GeneralMethods;
import org.bukkit.Material;
import com.projectkorra.projectkorra.ability.Ability;
import org.bukkit.boss.BarFlag;
import org.bukkit.boss.BarStyle;
import org.bukkit.boss.BarColor;
import org.bukkit.Bukkit;
import com.projectkorra.projectkorra.util.BlockSource;
import com.projectkorra.projectkorra.util.ClickType;
import com.projectkorra.projectkorra.PKListener;
import com.projectkorra.projectkorra.TLBMethods;
import com.projectkorra.projectkorra.util.StatisticsMethods;
import org.bukkit.GameMode;
import com.projectkorra.projectkorra.ability.CoreAbility;

import org.bukkit.entity.Entity;
import org.bukkit.entity.EntityType;
import org.bukkit.entity.Player;
import java.util.ArrayList;
import java.util.List;
import com.projectkorra.projectkorra.util.TempBlock;
import org.bukkit.Location;
import org.bukkit.boss.BossBar;
import com.projectkorra.projectkorra.ability.ComboAbility;
import com.projectkorra.projectkorra.ability.AddonAbility;
import com.projectkorra.projectkorra.ability.WaterAbility;

public class Condensation extends WaterAbility implements AddonAbility, ComboAbility
{
    private BossBar chargeBar;
    private double chargeAmount;
    private double chargeCount;
    private boolean started;
    private boolean formedSource;
    private Location center;
    private TempBlock spinningBlock;
    private double radius;
    private double angle;
    private TempBlock taperedSourceBlock;
    private double mistCharge;
    private double rainCharge;
    private double sourceCharge;
    private double selectRange;
    private long currentLevel;
    private long cooldown;
    public static List<TempBlock> sourcedBlocks;
    public static List<TempBlock> taperedSource;
    
    static {
        Condensation.sourcedBlocks = new ArrayList<TempBlock>();
        Condensation.taperedSource = new ArrayList<TempBlock>();
    }
    
    public Condensation(final Player player) {
        super(player);
        this.chargeAmount = 0.025;
        this.angle = 3.141592653589793;
        if (CoreAbility.hasAbility(player, (Class)Condensation.class)) {
            return;
        }
        if (this.player.isDead() || !this.player.isOnline() || this.player.getGameMode().equals((Object)GameMode.SPECTATOR)) {
            return;
        }
        if (this.bPlayer == null) {
            return;
        }
        if (this.bPlayer.isOnCooldown("Condensation")) {
            return;
        }
        if (!this.bPlayer.canBendIgnoreBindsCooldowns((CoreAbility)this)) {
            return;
        }
        final int statLevel = StatisticsMethods.getId("AbilityLevel_Condensation");
        this.currentLevel = TLBMethods.limitLevels(player, statLevel);
        this.cooldown = TLBMethods.getLong("Abilities.Water.Condensation.Cooldown", this.currentLevel);
        this.radius = (double)TLBMethods.getLong("Abilities.Water.Condensation.BuddyDistance", this.currentLevel);
        this.mistCharge = TLBMethods.getDouble("Abilities.Water.Condensation.MistChargeSpeed", this.currentLevel);
        this.rainCharge = TLBMethods.getDouble("Abilities.Water.Condensation.RainChargeSpeed", this.currentLevel);
        this.sourceCharge = TLBMethods.getDouble("Abilities.Water.Condensation.SourceChargeSpeed", this.currentLevel);
        this.chargeAmount = TLBMethods.getDouble("Abilities.Water.Condensation.BaseChargeSpeed", this.currentLevel);
        this.selectRange = (double)TLBMethods.getLong("Abilities.Water.Condensation.SourceRange", this.currentLevel);
        if (!PKListener.remoteWaterSource.containsKey(player)) {
            this.start();
        }
    }

    private boolean isInsideMist(Entity entity) {
        String entityName = entity.getName();
        if (entity.getType() == EntityType.INTERACTION && entityName.substring(0,4).equals("MIST")) { //if they are a MIST marker
            if ( Double.parseDouble( entityName.substring(5,entityName.length())) >= player.getLocation().distance(entity.getLocation()) )
            return true;
        }
        return false;
    }

    private boolean checkMist() {
        List<Entity> validEntities = GeneralMethods.getEntitiesAroundPoint(player.getLocation(), 25) //25 because no Mist should ever go above that radius
            .stream()
            .filter(this::isInsideMist)
            .toList();
        if (validEntities.size() >= 1) { //at least one valid mist in range
            return true;
        }
        return false;
    }
    
    public void progress() {
        if (this.player.isSneaking()) {
            if (!this.started) {
                if (checkMist()) {
                    this.chargeAmount = this.mistCharge;
                }
                if (this.player.getWorld().hasStorm()) {
                    this.chargeAmount = this.rainCharge;
                }
                else if (BlockSource.getWaterSourceBlock(this.player, this.selectRange, ClickType.SHIFT_DOWN, true, true, true) != null) {
                    this.chargeAmount = this.sourceCharge;
                }
                else {
                    this.chargeAmount = this.chargeAmount;
                }
                if (!this.bPlayer.getBoundAbilityName().equals("WaterManipulation")) {
                    return;
                }
                this.started = true;
                this.chargeCount = 0.0;
                (this.chargeBar = Bukkit.getServer().createBossBar("", BarColor.WHITE, BarStyle.SOLID, new BarFlag[0])).setProgress(0.0);
                this.chargeBar.addPlayer(this.player);
            }
            else {
                this.updateChargeBarProgress();
                if (this.chargeBar.getProgress() == 1.0) {
                    this.chargeBar.setColor(BarColor.BLUE);
                    this.chargeBar.setTitle("CHARGED!");
                }
            }
        }
        else if (!this.formedSource) {
            this.remove();
            return;
        }
        if (this.formedSource) {
            this.center = this.player.getLocation().add(0.0, 2.0, 0.0);
            this.displayParticles();
        }
    }
    
    public void remove() {
        if (this.started) {
            this.chargeBar.removeAll();
        }
        if (!Condensation.taperedSource.isEmpty()) {
            Condensation.taperedSource.get(0).revertBlock();
            Condensation.taperedSource.clear();
        }
        if (!Condensation.sourcedBlocks.isEmpty()) {
            Condensation.sourcedBlocks.get(0).revertBlock();
            Condensation.sourcedBlocks.clear();
        }
        PKListener.remoteWaterSource.remove(this.player);
        super.remove();
    }
    
    public void formSource() {
        if (this.chargeBar.getProgress() == 1.0 && !PKListener.remoteWaterSource.containsKey(this.player)) {
            this.chargeBar.removeAll();
            this.bPlayer.addCooldown((Ability)this);
            this.formedSource = true;
        }
    }
    
    public void displayParticles() {
        if (!this.bPlayer.canBendIgnoreBindsCooldowns((CoreAbility)this)) {
            this.remove();
            return;
        }
        final Block rightBlock = this.player.getEyeLocation().add(getRightHeadDirection(this.player).multiply(this.radius)).getBlock();
        if (rightBlock.getType() == Material.AIR || rightBlock.getType() == Material.CAVE_AIR || rightBlock.getType() == Material.WATER) {
            if (!Condensation.taperedSource.isEmpty()) {
                Condensation.taperedSource.get(0).revertBlock();
                Condensation.taperedSource.clear();
            }
            if (!Condensation.sourcedBlocks.isEmpty()) {
                if (!Condensation.sourcedBlocks.get(0).getLocation().equals((Object)rightBlock.getLocation())) {
                    this.taperedSourceBlock = new TempBlock(Condensation.sourcedBlocks.get(0).getLocation().getBlock(), GeneralMethods.getWaterData(7));
                    Condensation.taperedSource.add(this.taperedSourceBlock);
                }
                else {
                    Condensation.sourcedBlocks.get(0).revertBlock();
                }
            }
            Condensation.sourcedBlocks.clear();
            this.spinningBlock = new TempBlock(rightBlock, Material.WATER);
            Condensation.sourcedBlocks.add(this.spinningBlock);
            PKListener.remoteWaterSource.put(this.player, this.spinningBlock);
        }
    }
    
    public static Vector getRightHeadDirection(final Player player) {
        final Vector direction = player.getLocation().getDirection().normalize();
        return new Vector(-direction.getZ(), 0.0, direction.getX()).normalize();
    }
    
    private void updateChargeBarProgress() {
        if (this.started) {
            if (this.chargeBar.getProgress() + this.chargeAmount <= 0.9999999) {
                this.chargeBar.setProgress(this.chargeBar.getProgress() + this.chargeAmount);
                this.chargeBar.setTitle(String.valueOf((int)(this.chargeBar.getProgress() * 100.0)) + "%");
            }
            else {
                this.chargeBar.setProgress(1.0);
            }
        }
    }
    
    public Object createNewComboInstance(final Player player) {
        return new Condensation(player);
    }
    
    public ArrayList<ComboManager.AbilityInformation> getCombination() {
        final ArrayList<ComboManager.AbilityInformation> combo = new ArrayList<ComboManager.AbilityInformation>();
        combo.add(new ComboManager.AbilityInformation("WaterManipulation", ClickType.SHIFT_DOWN));
        combo.add(new ComboManager.AbilityInformation("WaterManipulation", ClickType.SHIFT_UP));
        combo.add(new ComboManager.AbilityInformation("WaterManipulation", ClickType.SHIFT_DOWN));
        return combo;
    }
    
    public String getDescription() {
        return "Concentrate and draw water out of the air to create a portable water source! When used in the rain, it will charge almost instantly. Carry the water source around with you until you need it, or use it to block an incoming attack!";
    }
    
    public String getInstructions() {
        return "WaterManipulation (Tap Sneak) > WaterManipulation (Hold Sneak) > Punch when charged to create a water source.";
    }
    
    public String getAuthor() {
        return "TLB";
    }
    
    public String getVersion() {
        return "1.1";
    }
    
    public void load() {
        ConfigManager.getConfig().addDefault("Abilities.Water.Condensation.Cooldown", (Object)250);
        ConfigManager.getConfig().addDefault("Abilities.Water.Condensation.BuddyDistance", (Object)2);
        ConfigManager.getConfig().addDefault("Abilities.Water.Condensation.MistChargeSpeed", (Object)0.1);
        ConfigManager.getConfig().addDefault("Abilities.Water.Condensation.RainChargeSpeed", (Object)0.025);
        ConfigManager.getConfig().addDefault("Abilities.Water.Condensation.SourceChargeSpeed", (Object)0.0175);
        ConfigManager.getConfig().addDefault("Abilities.Water.Condensation.SourceRange", (Object)4);
        ConfigManager.getConfig().addDefault("Abilities.Water.Condensation.BaseChargeSpeed", (Object)0.005);
        ProjectKorra.plugin.getServer().getPluginManager().registerEvents((Listener)new CondensationListener(), (Plugin)ProjectKorra.plugin);
        ConfigManager.defaultConfig.save();
    }
    
    public void stop() {
        ProjectKorra.log.info(String.valueOf(String.valueOf(this.getName())) + " " + this.getVersion() + " by " + this.getAuthor() + " disabled! ");
        super.remove();
    }
    
    public long getCooldown() {
        return this.cooldown;
    }
    
    public Location getLocation() {
        return null;
    }
    
    public String getName() {
        return "Condensation";
    }
    
    public boolean isHarmlessAbility() {
        return false;
    }
    
    public boolean isSneakAbility() {
        return false;
    }
}