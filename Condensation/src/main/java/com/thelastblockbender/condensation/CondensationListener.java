package com.thelastblockbender.condensation;

import org.bukkit.event.EventPriority;
import org.bukkit.event.entity.EntityDeathEvent;
import org.bukkit.entity.Player;
import com.projectkorra.projectkorra.PKListener;
import org.bukkit.event.player.PlayerQuitEvent;
import org.bukkit.event.EventHandler;
import com.projectkorra.projectkorra.ability.CoreAbility;
import org.bukkit.event.player.PlayerAnimationEvent;
import org.bukkit.event.Listener;

public class CondensationListener implements Listener
{
    @EventHandler
    public void onSwing(final PlayerAnimationEvent event) {
        if (event.isCancelled()) {
            return;
        }
        if (CoreAbility.hasAbility(event.getPlayer(), (Class)Condensation.class)) {
            final Condensation abil = (Condensation)CoreAbility.getAbility(event.getPlayer(), (Class)Condensation.class);
            abil.formSource();
        }
    }
    
    @EventHandler
    public void onPlayerQuit(final PlayerQuitEvent event) {
        final Player player = event.getPlayer();
        if (PKListener.remoteWaterSource.contains<PERSON>ey(player)) {
            PKListener.remoteWaterSource.remove(player);
            final CoreAbility playerAbility = CoreAbility.getAbility(player, (Class)CoreAbility.getAbility("Condensation").getClass());
            playerAbility.remove();
        }
    }
    
    @EventHandler(priority = EventPriority.NORMAL, ignoreCancelled = true)
    public void onEntityDeath(final EntityDeathEvent event) {
        if (event.getEntity() instanceof Player) {
            final Player player = (Player)event.getEntity();
            if (PKListener.remoteWaterSource.containsKey(player)) {
                PKListener.remoteWaterSource.remove(player);
                final CoreAbility playerAbility = CoreAbility.getAbility(player, (Class)CoreAbility.getAbility("Condensation").getClass());
                playerAbility.remove();
            }
        }
    }
}