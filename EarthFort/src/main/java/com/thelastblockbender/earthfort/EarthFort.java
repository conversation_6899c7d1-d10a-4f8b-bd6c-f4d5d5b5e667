package com.thelastblockbender.earthfort;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ThreadLocalRandom;
import java.lang.Math;

import com.projectkorra.projectkorra.BendingPlayer;
import com.projectkorra.projectkorra.Element;
import com.projectkorra.projectkorra.GeneralMethods;
import com.projectkorra.projectkorra.ProjectKorra;
import com.projectkorra.projectkorra.TLBMethods;
import com.projectkorra.projectkorra.ability.AddonAbility;
import com.projectkorra.projectkorra.ability.EarthAbility;
import com.projectkorra.projectkorra.ability.SandAbility;
import com.projectkorra.projectkorra.ability.LavaAbility;
import com.projectkorra.projectkorra.configuration.ConfigManager;
import com.projectkorra.projectkorra.util.StatisticsMethods;
import com.projectkorra.projectkorra.util.TempBlock;
import com.projectkorra.projectkorra.util.ParticleEffect;
import org.bukkit.Bukkit;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.Particle;
import org.bukkit.Sound;
import org.bukkit.Vibration;
import org.bukkit.World;
import org.bukkit.block.Block;
import org.bukkit.block.BlockFace;
import org.bukkit.block.data.BlockData;
import org.bukkit.entity.Entity;
import org.bukkit.entity.EntityType;
import org.bukkit.entity.LivingEntity;
import org.bukkit.entity.Player;
import org.bukkit.event.player.PlayerInteractEvent;
import org.bukkit.inventory.ItemStack;
import org.bukkit.util.Vector;
import org.bukkit.ChatColor;

import net.kyori.adventure.text.Component;

//builder method imports
import com.sk89q.worldedit.bukkit.BukkitAdapter;
import com.sk89q.worldedit.extent.clipboard.Clipboard;
import com.sk89q.worldedit.extent.clipboard.io.ClipboardFormat;
import com.sk89q.worldedit.extent.clipboard.io.ClipboardFormats;
import com.sk89q.worldedit.extent.clipboard.io.ClipboardReader;
import com.sk89q.worldedit.math.BlockVector3;
import com.sk89q.worldedit.world.block.BaseBlock;
import com.sk89q.worldedit.world.block.BlockState;
import com.sk89q.worldedit.world.block.BlockType;
import com.sk89q.worldedit.world.block.BlockTypes;
import com.sk89q.worldedit.registry.state.Property;
import com.sk89q.worldedit.session.ClipboardHolder;
import com.sk89q.worldedit.math.transform.AffineTransform;
import com.sk89q.worldedit.math.Vector3;


import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.Arrays;
import java.util.stream.Collectors;

public class EarthFort extends EarthAbility implements AddonAbility {
  private static Clipboard schematic = null;
  private Location location;

  enum Stage {PREP, BUILDING, BUILT}
  enum FortType {STONE, MOAT, SAND, LAVA} //MOAT state is unused - theoretical state where stone efort gets a lava moat for lavabenders

  private long cooldown;
  private double radius;
  private long duration;
  private double delay;
  private double illegalThreshold; //highest % of blocks that can be illegal flooring while allowing efort
  private long interval; //time between each layer being built (in milliseconds)
  private Location fortLocation;
  private Entity fortMarker; //stores interaction entity to be removed later
  private long lastBlockTime;
  private Stage stage = Stage.PREP;
  private int fortHeight = 1;
  private FortType fortType;
  private int rotation = 0; //degrees of rotation of the efort schematic
  private BlockData buildParticle; //set to sand/stone/blackstone to animate build particle properly based on terrain assessment
  private static Map<BlockType,BlockType> sandMap;
  private static Map<BlockType,BlockType> blackstoneMap;
  // Add a map to store pre-loaded schematics
  private static Map<String, Clipboard> schematicCache;

  private BlockVector3 schemDim;

  public EarthFort(Player player) {
    super(player);
    if (!bPlayer.canBend(this)) return; //ensure player bending is valid

    setFields(); //import values
    if (prepare()) { //if prepare method runs without problems, start the move but do not add cooldown - that only happens if the fort gets summoned    
      start();
    }
  }

  public void setFields() { //import values
    int statLevel = StatisticsMethods.getId("AbilityLevel_" + this.getName());
    long currentLevel = TLBMethods.limitLevels(player, statLevel);

    cooldown = TLBMethods.getLong("ExtraAbilities.Thel.Earth.EarthFort.Cooldown", currentLevel);
    radius = TLBMethods.getDouble("ExtraAbilities.Thel.Earth.EarthFort.Radius", currentLevel);
    duration = TLBMethods.getLong("ExtraAbilities.Thel.Earth.EarthFort.Duration", currentLevel);
    delay = TLBMethods.getDouble("ExtraAbilities.Thel.Earth.EarthFort.ChargeTime", currentLevel);
    illegalThreshold = TLBMethods.getDouble("ExtraAbilities.Thel.Earth.EarthFort.IllegalThreshold", currentLevel);
    interval = TLBMethods.getLong("ExtraAbilities.Thel.Earth.EarthFort.BuildInterval", currentLevel);
  }

//FILTER METHODS ----

  private boolean isValidTarget(Player player) { //can this player see charge particles?
    if (BendingPlayer.getBendingPlayer(player).canBendIgnoreBinds(this)) { //if they can use Earthfort
      return true;
    }
    return false;
  }

  private boolean isNearbyFort(Entity entity) { //is an earthfort marker within range?
    if (entity.getType() == EntityType.INTERACTION && entity.getName().equals("EARTHFORT")) { //if they are a marker with the name EARTHFORT
      return true;
    }
    return false;
  }

  private boolean isHelping(Player otherPlayer) { //are any OTHER players within range helping w/ the earthfort summon?

    if (otherPlayer.getUniqueId().equals(player.getUniqueId())) { //if the 'other player' is the caster, do not include them as 'helping'
      return false;
    }
    
    BendingPlayer bPlayer = BendingPlayer.getBendingPlayer(otherPlayer);
    if (bPlayer.getBoundAbilityName().equalsIgnoreCase("EarthFort") & otherPlayer.isSneaking()) { //if they are holding earthfort & shifting
      return true;
    }
    return false;
  }

  private boolean isException(Block targetBlock) { //method which checks if a block, despite not being earthbendable, should still be replaceable by earthfort blocks (flowers, cobwebs, etc)
    //only put blocks that are unimportant, that can be replaced without issue, here.
    return false;
  }
  
  //-----------------

  private void spawnEarthFortParticleRing() {

    //build ring

    final float spread = 2;
    final float depth = 0.05f;
    Location ploc = player.getLocation();
    double plocX = ploc.getX();
    double plocY = ploc.getY();
    double plocZ = ploc.getZ();
    double targetX;
    double targetY = plocY + 1.0;
    double targetZ;

    //get list of players who will see the particles (only earthbenders)
    List<Player> playerList = GeneralMethods.getPlayersAroundPoint(ploc, 30) //30 block radius in which fellow earthbenders can see the particles
      .stream()
      .filter(this::isValidTarget)
      .toList(); 

    //draw ring
    for (int i=0;i<16;i++) {
      targetX = plocX + spread * Math.cos(Math.toRadians((i)*(360/16)));
      targetZ = plocZ + spread * Math.sin(Math.toRadians((i)*(360/16)));

      //determine color of ring
      if (System.currentTimeMillis()-this.getStartTime() >= (delay)) { // ready
        player.getWorld().spawnParticle(Particle.TOTEM_OF_UNDYING, playerList,null,targetX,targetY,targetZ,1,0.0,0.05, 0.05,0.05,null);}
      else if (System.currentTimeMillis()-this.getStartTime() >= (delay/2)) { //halfway
        player.getWorld().spawnParticle(Particle.CRIT, playerList,null,targetX,targetY,targetZ,1,0.0,0.1, 0.1,0.1,null);}
      else { player.getWorld().spawnParticle(Particle.ASH, playerList,null,targetX,targetY,targetZ,10,0.0,0.2, 0.2,0.2,null);} //less than halfway
    }
  }

  private int evaluateTerrain(int sizeScore) { //evaluate the local region based on the size of the fort
    int returnVal = 0; //default to illegal terrain

    //determine how big of a cube to search
    int searchCubeLength = 1;
    switch (sizeScore) {
      case 1 -> searchCubeLength = 9; //arbitrary sizes of the actual schematics for each size
      case 2 -> searchCubeLength = 17;
      case 3 -> searchCubeLength = 21;
      case 4 -> searchCubeLength = 21;
      case 5 -> searchCubeLength = 31;
    }

    World w = player.getWorld();
    Location ploc = player.getLocation().getBlock().getLocation(); //get location of block
    int centerX = (int)ploc.getX(); //floor these values
    int centerY = (int)ploc.getY();
    int centerZ = (int)ploc.getZ();

    boolean canSandbend = BendingPlayer.getBendingPlayer(player).hasElement(Element.SAND);
    boolean canLavabend = BendingPlayer.getBendingPlayer(player).hasElement(Element.LAVA);

    int lavaBlocks = 0; // lava and magma blocks
    int sandBlocks = 0; //sand and variants
    int illegalBlocks = 0; //anything else!
    int validBlocks = 0; //air, earthbendable blocks, some exceptions (i.e. flowers, grass, stuff like that, to be added into isException())


    //search the square base of the castle (must be mostly solid)
    int halfSide = searchCubeLength / 2;

    for (int x = centerX - halfSide; x <= centerX + halfSide; x++) {
      for (int z = centerZ - halfSide; z <= centerZ + halfSide; z++) {
        Block targetBlock = new Location(w,x,centerY - 1,z).getBlock();
        if (isSandbendable(targetBlock)) {
          sandBlocks +=1;
        }
        else if (isLavabendable(targetBlock) || targetBlock.getBlockData().getMaterial() == Material.MAGMA_BLOCK) {
          lavaBlocks +=1;
        }
        else if (isEarthbendable(targetBlock) || isException(targetBlock)) {
          validBlocks +=1;
        }
        else {illegalBlocks +=1;}
      }
    }

    //debug: System.out.println("EARTHFORT: Illegal: "+illegalBlocks+"\nValid: "+validBlocks+"\nSand: "+sandBlocks+"\nLava: "+lavaBlocks);

    //determine proportion of floor that is valid
    int trueValidBlocks = validBlocks;
    if (canSandbend) {trueValidBlocks += sandBlocks;}
    if (canLavabend) {trueValidBlocks += lavaBlocks;}

    if ( ( (double) illegalBlocks) / (trueValidBlocks + illegalBlocks) > illegalThreshold) {returnVal = 0;} //no fort
    else {returnVal = 1;}
    if (lavaBlocks >= sandBlocks) {
      if (lavaBlocks > validBlocks & canLavabend) {returnVal = 3;} //blackstone fort
    }
    else if (sandBlocks > lavaBlocks) {
      if (sandBlocks > validBlocks & canSandbend) {returnVal = 2;} //sandy fort
    }
    //now that a valid floor and fort type have been established, ensure it has the room to grow (check cube above floor)

    //search the cube

    for (int x = centerX - halfSide; x <= centerX + halfSide; x++) {
      for (int y = centerY; y <= centerY + searchCubeLength; y++) { //only search above, not below
        for (int z = centerZ - halfSide; z <= centerZ + halfSide; z++) {
          Block targetBlock = new Location(w,x,y,z).getBlock();
          if (!(isEarthbendable(targetBlock) || targetBlock.isLiquid() || targetBlock.isPassable()|| isException(targetBlock)) ) {return 0;}
        }
      }
    } 
    return returnVal;
  }

  private BlockData materialAdapter(BlockState inputBlock, FortType fortType) { //assign correct fort block type
    BlockState newBlockState;
    BlockType newBlockType;

    if (fortType == FortType.SAND ) {
      newBlockType = sandMap.get(inputBlock.getBlockType()); }
    else if (fortType == FortType.LAVA) {//blackstone
      newBlockType = blackstoneMap.get(inputBlock.getBlockType()); }
    else { //stone
      return BukkitAdapter.adapt(inputBlock); } //no change from default schem
    
    newBlockState = newBlockType.getDefaultState(); //create blank propertly-less state out of desired blocktype
    
    //get all desired states
    Map<Property<?> ,Object> stateMap = inputBlock.getStates();

    //iterate through all states
    List<Property<?>> keys = new ArrayList<>(stateMap.keySet());
    List<Object> values = new ArrayList<>(stateMap.values());

    for (int i = 0; i < stateMap.size(); i++) {
      Property<Object> key = (Property<Object>) keys.get(i);
      Object value = values.get(i);

      //apply state to the new blockState
      newBlockState = newBlockState.with(key,value);
    }

    return BukkitAdapter.adapt(newBlockState);
  }

  private Clipboard readSchematic(String filepath) {
    // Check if the schematic is already in the cache
    if (schematicCache.containsKey(filepath)) {
      return schematicCache.get(filepath);
    }
    
    // If not in cache (shouldn't happen if preloaded correctly), load it
    try {
      File file = new File(new File(ProjectKorra.plugin.getDataFolder(), "/Abilities/Schematics/EarthFortSchematics/"), filepath);
      ClipboardFormat format = ClipboardFormats.findByFile(file);
      if (format == null) {
        ProjectKorra.log.warning("[EarthFort] Unknown schematic format for file: " + filepath);
        return null;
      }
      
      try (ClipboardReader reader = format.getReader(new FileInputStream(file))) {
        Clipboard clipboard = reader.read();
        // Add to cache for future use
        schematicCache.put(filepath, clipboard);
        return clipboard;
      }
    } catch (IOException e) {
      ProjectKorra.log.severe("[EarthFort] Couldn't load schematic " + filepath);
      ProjectKorra.log.severe(e.getMessage());
      return null;
    }
  }

  public void buildBelowY(Clipboard schematic, Location origin, int maxY, FortType fortType, boolean finalPhase) {
    Location absPos = new Location(player.getWorld(), origin.getX(), origin.getY(), origin.getZ());

    long revTime; // decide how long to show blocks for
    if (finalPhase) {revTime = duration;}
    else {revTime = interval - 50;}
    
    for (int y = 0; y < maxY; y++) {
      for (int x = 0; x < schemDim.getX(); x++) {
        for (int z = 0; z < schemDim.getZ(); z++) {
          // for each block in the build zone:
          BlockState schemBlock = schematic.getBlock(BlockVector3.at(
              schematic.getMinimumPoint().getX() + x, 
              schematic.getMaximumPoint().getY() - y, 
              schematic.getMinimumPoint().getZ() + z)); // get the matching block in the schematic (using relative coords)
              
          if (!schemBlock.getBlockType().getMaterial().isAir()) { // if the schem_block isnt air
            // place the schem block in the real world (using absolute coords)
            Location finalPos = new Location(player.getWorld(), absPos.getX()+x, absPos.getY()-y+maxY-1, absPos.getZ()+z);
            Block targetBlock = finalPos.getBlock();
            
            // Skip protected blocks
            if (isProtectedBlock(targetBlock)) {
              continue;
            }
            
            BlockData mat = materialAdapter(schemBlock, fortType);
            // intercept materials here and convert to stone/sand/blackstone as required
            new TempBlock(targetBlock, mat).setRevertTime(revTime); // revert just before placing the next round of blocks

            if (ThreadLocalRandom.current().nextDouble() <= 0.2) { // 20% of blocks being built will emit particles & sound
              // draw particles & play sounds
              ParticleEffect.BLOCK_DUST.display(finalPos, 10, 0.5, 0.5, 0.5, 0, buildParticle);
              player.getWorld().playSound(finalPos, Sound.ENTITY_GHAST_SHOOT, 0.05f, 0.5f);
            }
          }
        }
      }
    }

    // draw particles & play sounds
    ParticleEffect.BLOCK_DUST.display(origin, 10, 0.5, 0.5, 0.5, 0, buildParticle);
    player.getWorld().playSound(origin, Sound.ENTITY_GHAST_SHOOT, 0.05f, 0.5f);

    lastBlockTime = System.currentTimeMillis();
  }

  private char getRotation(int fortSize) { //quick method to grab a character for the filepath building
    char returnVal = 'N'; //default to north
    if (fortSize >= 4) {return returnVal;} //fort sizes 4 & 5 are perfectly symmetrical, no need for rotations
    switch (rotation) {
      case 90 -> returnVal = 'E';
      case 180 -> returnVal = 'S';
      case 270 -> returnVal = 'W';
    }
    return returnVal;
  }

  private void buildFort(int sizeScore, FortType fortType) {
    //at playerlocation, build fort
    //debug: System.out.println("EARTHFORT: Player "+player.getName()+" is building an EarthFort of size "+Integer.toString(sizeScore)+".");

    //select the right schematic:
    String filepath = "EarthFort_Size_" + sizeScore + "_" + getRotation(sizeScore) + ".schem";
    Clipboard schematic = readSchematic(filepath);
    
    // Check if schematic was loaded successfully
    if (schematic == null) {
      player.sendMessage(ChatColor.RED + "Failed to load fort schematic. Please contact an administrator.");
      remove();
      return;
    }
    
    schemDim = schematic.getDimensions();

    // Check if the area is protected from building
    if (!canBuildInArea(fortLocation, schemDim)) {
        // Notify all players involved in the summon
        player.sendMessage(ChatColor.RED + "Sorry, you can't build here so this move was cancelled.");
        
        // Find and notify all helping players
        List<Player> helpingPlayers = GeneralMethods.getPlayersAroundPoint(player.getLocation(), radius)
            .stream()
            .filter(this::isHelping)
            .toList();
            
        for (Player helper : helpingPlayers) {
            helper.sendMessage(ChatColor.RED + "Sorry, you can't build here so this move was cancelled.");
        }
        
        remove();
        return;
    }

    fortLocation.add(-schemDim.getX()/2, 0, -schemDim.getZ()/2); //offset position to centralize fort on player
    stage = Stage.BUILDING; //initiate build phase
  }

  /**
   * Checks if the player can build in the entire area covered by the schematic.
   * Tests the corners of the bounding box for build protection.
   * 
   * @param origin The origin location of the fort
   * @param dimensions The dimensions of the schematic
   * @return true if building is allowed in all corners, false otherwise
   */
  private boolean canBuildInArea(Location origin, BlockVector3 dimensions) {
    World world = origin.getWorld();
    double originX = origin.getX() - dimensions.getX()/2;
    double originY = origin.getY();
    double originZ = origin.getZ() - dimensions.getZ()/2;
    
    // Create an array of the 8 corners of the bounding box
    Location[] corners = new Location[8];
    corners[0] = new Location(world, originX, originY, originZ);
    corners[1] = new Location(world, originX + dimensions.getX(), originY, originZ);
    corners[2] = new Location(world, originX, originY + dimensions.getY(), originZ);
    corners[3] = new Location(world, originX, originY, originZ + dimensions.getZ());
    corners[4] = new Location(world, originX + dimensions.getX(), originY + dimensions.getY(), originZ);
    corners[5] = new Location(world, originX + dimensions.getX(), originY, originZ + dimensions.getZ());
    corners[6] = new Location(world, originX, originY + dimensions.getY(), originZ + dimensions.getZ());
    corners[7] = new Location(world, originX + dimensions.getX(), originY + dimensions.getY(), originZ + dimensions.getZ());
    
    // Check each corner for build protection
    for (Location corner : corners) {
        if (GeneralMethods.isRegionProtectedFromBuild(player, corner)) {
            return false;
        }
    }
    
    return true;
  }

  @Override
  public void progress() {

    switch (stage) { //borrowed this neat structure from razorring
      case PREP -> progressPrep(); //all code that runs before the fort begins building
      case BUILDING -> progressBuilding(); //all code that runs during fort building
      case BUILT -> progressBuilt(); //all code that runs at the very end of the move, setting it as a permanent structure(?)
    }

  }

  private void progressPrep() {
    if (!bPlayer.canBend(this)) { //ensure user can still bend currently
      remove();
      return;
    } 

    if (!player.isSneaking()) { //if player stops sneaking
      if (System.currentTimeMillis() - this.getStartTime() < delay) {remove(); return;} //abort move if not fully charged
      //if a Marker with an EARTHFORT name is nearby, abort the creation process (too close!)
      List<Entity> entities = GeneralMethods.getEntitiesAroundPoint(player.getLocation(), 40) //fort centers must be more than 40 blocks apart
        .stream()
        .filter(this::isNearbyFort)
        .toList(); 
      if (entities.size() > 0) {//if any forts are too close
        //display particles & sound to indicate move failure
        ParticleEffect.SMOKE.display(player.getLocation(), 10, 0.5, 0.5, 0.5, 0, buildParticle);
        player.playSound(player.getLocation(),Sound.BLOCK_FIRE_EXTINGUISH, 0.3f, 0.5f);
        remove(); //end move
        return;
      }
      //if a player that is currently shifting on earthfort is in close range, add them to the size score
      List<Player> playerList = GeneralMethods.getPlayersAroundPoint(player.getLocation(), radius).stream().toList();
      playerList = playerList
        .stream()
        .filter(this::isHelping)
        .toList(); 
      int sizeScore = playerList.size() + 1; //# of players who are helping with the earthfort. +1 is for the user themselves.

      int terrainState = evaluateTerrain(sizeScore);
      //code: 0 = unusable terrain (illegal obstructions, lack of floor)
      // 1 = standard valid terrain
      // 2 = excessively sandy terrain (requires sandbending to use)
      // 3 = excessively lava-ey terrain (requires lavabending to use)

      if (terrainState == 0) {
        //display particles to indicate move failure
        ParticleEffect.SMOKE.display(player.getLocation(), 10, 0.5, 0.5, 0.5, 0, buildParticle);
        player.getWorld().playSound(player.getLocation(),Sound.BLOCK_FIRE_EXTINGUISH, 0.5f, 0.5f);
        remove(); return;} //if invalid terrain, end move

      else if (terrainState == 1) { //if standard valid terrain
        fortType = FortType.STONE; //standard stone fort
        buildParticle = Material.STONE.createBlockData();
        //unused feature:
        /*if (BendingPlayer.getBendingPlayer(player).hasElement(Element.LAVA)) { //if they can lavabend
          fortType = FortType.MOAT; //add lava moat
        }*/
      }

      else if (terrainState == 2) { //if sandy terrain:

        if (BendingPlayer.getBendingPlayer(player).hasElement(Element.SAND)) { //if they can sandbend
          fortType = FortType.SAND;
          buildParticle = Material.SAND.createBlockData();

        } else {
          //display particles to indicate move failure
          ParticleEffect.SMOKE.display(player.getLocation(), 10, 0.5, 0.5, 0.5, 0, buildParticle);
          player.getWorld().playSound(player.getLocation(),Sound.BLOCK_FIRE_EXTINGUISH, 0.5f, 0.5f);
          remove();return;} //if they cannot sandbend, end move
      }
      else if (terrainState == 3) { //if excessively lava-ey terrain

        if (BendingPlayer.getBendingPlayer(player).hasElement(Element.LAVA)) { //if they can lavabend
          fortType = FortType.LAVA;
          buildParticle = Material.MAGMA_BLOCK.createBlockData();

        } else {
          //display particles to indicate move failure
          ParticleEffect.SMOKE.display(player.getLocation(), 10, 0.5, 0.5, 0.5, 0, buildParticle);
          player.getWorld().playSound(player.getLocation(),Sound.BLOCK_FIRE_EXTINGUISH, 0.5f, 0.5f);
          remove();return;} //if they cannot lavabend, end move
      }

      //mark the location as having an earth fort [spawn interaction entity]
      fortMarker = player.getWorld().spawnEntity(player.getLocation(),EntityType.INTERACTION);
      Component markerName = Component.text("EARTHFORT");
      fortMarker.customName(markerName);

      //set location of fort (it is now set in stone [get it])
      fortLocation = player.getLocation();

      //determine orientation fort should have
      float yaw = player.getLocation().getYaw();
      yaw = (yaw - 180) % 360;
      if (yaw < 0) {
        yaw += 360;
      }

      ////rotate to follow player's orientation
      if (yaw >= 315 || yaw < 45) {
        //north - no schematic rotation needed
      } else if (yaw >= 45 && yaw < 135) { //east
        rotation = 90;
      } else if (yaw >= 135 && yaw < 225) { //south
        rotation = 180;
      } else if (yaw >= 225 && yaw < 315) { //west
        rotation = 270;
      }

      //run build method with size score.
      buildFort(sizeScore,fortType);

    } else {
      //otherwise:
      spawnEarthFortParticleRing(); //spawn a particle ring around the player to indicate the summon radius (radius in which fellow earthbenders can assist)
    }
  }

  private void progressBuilding() {
    if (System.currentTimeMillis() - lastBlockTime >= interval) {
      
      if (fortHeight >= schemDim.getY()) {
        stage = Stage.BUILT;
      } else {
        buildBelowY(schematic, fortLocation, fortHeight, fortType, false);
        lastBlockTime = System.currentTimeMillis();
        fortHeight ++;}
    } 
  }

  private void progressBuilt() {
    buildBelowY(schematic, fortLocation, fortHeight, fortType, true);

    //add cooldown & end move
    bPlayer.addCooldown(this);
    remove();
    return;
  }

  public boolean prepare() {
    location = player.getLocation();

    Block block = location.getBlock();
    if (block.isLiquid() || !isTransparent(block)) { //check if not standing in clear space
      return false;
    }
    return true;
  }

  @Override
  public long getCooldown() {
    return cooldown;
  }

  @Override
  public Location getLocation() {
    return location;
  }

  @Override
  public String getName() {
    return "EarthFort";
  }

  @Override
  public String getDescription() {
    return "This ability allows earthbenders to combine their strength to rearrange their environment and form a mighty shelter of earth.";
  }

  @Override
  public String getInstructions() {
    return "Hold shift to charge up an Earth Fort, then release. If multiple earthbenders charge this move while standing close together, the fort produced will be larger.";
  }

  @Override
	public boolean isSneakAbility() {
		return true;
	}
  
  @Override
  public boolean isHarmlessAbility() {
    return false;
  }

  @Override
  public String getAuthor() {
    return "Thel";
  }

  @Override
  public String getVersion() {
    return "v1.0.1";
  }

  @Override
  public void load() {
    ProjectKorra.plugin.getServer().getPluginManager().registerEvents(new EarthFortListener(), ProjectKorra.plugin);
    ProjectKorra.log.info(getName() + " " + getVersion() + " by " + getAuthor() + " loaded!");

    ConfigManager.getConfig().addDefault("ExtraAbilities.Thel.Earth.EarthFort.Cooldown", 5000);
    ConfigManager.getConfig().addDefault("ExtraAbilities.Thel.Earth.EarthFort.Radius", 10);
    ConfigManager.getConfig().addDefault("ExtraAbilities.Thel.Earth.EarthFort.Duration", 10000);
    ConfigManager.getConfig().addDefault("ExtraAbilities.Thel.Earth.EarthFort.ChargeTime",3000);
    ConfigManager.getConfig().addDefault("ExtraAbilities.Thel.Earth.EarthFort.IllegalThreshold",0.15);
    ConfigManager.getConfig().addDefault("ExtraAbilities.Thel.Earth.EarthFort.BuildInterval",1000);
    ConfigManager.defaultConfig.save();
    
    // Initialize the block type maps once during loading
    sandMap = new HashMap<>();
    sandMap.put(BlockTypes.STONE, BlockTypes.SMOOTH_SANDSTONE);
    sandMap.put(BlockTypes.COBBLESTONE, BlockTypes.SANDSTONE);
    sandMap.put(BlockTypes.COBBLESTONE_WALL, BlockTypes.SANDSTONE_WALL);
    sandMap.put(BlockTypes.STONE_SLAB, BlockTypes.SMOOTH_SANDSTONE_SLAB);
    sandMap.put(BlockTypes.STONE_STAIRS, BlockTypes.SMOOTH_SANDSTONE_STAIRS);
    
    blackstoneMap = new HashMap<>();
    blackstoneMap.put(BlockTypes.STONE, BlockTypes.POLISHED_BLACKSTONE);
    blackstoneMap.put(BlockTypes.COBBLESTONE, BlockTypes.BLACKSTONE);
    blackstoneMap.put(BlockTypes.COBBLESTONE_WALL, BlockTypes.BLACKSTONE_WALL);
    blackstoneMap.put(BlockTypes.STONE_SLAB, BlockTypes.POLISHED_BLACKSTONE_SLAB);
    blackstoneMap.put(BlockTypes.STONE_STAIRS, BlockTypes.POLISHED_BLACKSTONE_STAIRS);
    
    // Initialize schematic cache
    schematicCache = new HashMap<>();
    
    // Pre-load all schematics
    preloadSchematics();
  }
  
  /**
   * Pre-loads all fort schematics into memory for faster access
   */
  private void preloadSchematics() {
    // Define all possible fort sizes and rotations
    int[] sizes = {1, 2, 3, 4, 5};
    char[] rotations = {'N', 'E', 'S', 'W'};
    
    for (int size : sizes) {
      // For sizes 4 and 5, only load the North orientation as they're symmetrical
      if (size >= 4) {
        String filepath = "EarthFort_Size_" + size + "_N.schem";
        loadSchematicToCache(filepath);
      } else {
        // For smaller sizes, load all rotations
        for (char rotation : rotations) {
          String filepath = "EarthFort_Size_" + size + "_" + rotation + ".schem";
          loadSchematicToCache(filepath);
        }
      }
    }
    
    ProjectKorra.log.info("[EarthFort] Loaded " + schematicCache.size() + " schematics into memory");
  }
  
  /**
   * Loads a schematic file into the cache
   * 
   * @param filepath The name of the schematic file
   */
  private void loadSchematicToCache(String filepath) {
    try {
      File file = new File(new File(ProjectKorra.plugin.getDataFolder(), "/Abilities/Schematics/EarthFortSchematics/"), filepath);
      if (!file.exists()) {
        ProjectKorra.log.warning("[EarthFort] Schematic file not found: " + filepath);
        return;
      }
      
      ClipboardFormat format = ClipboardFormats.findByFile(file);
      if (format == null) {
        ProjectKorra.log.warning("[EarthFort] Unknown schematic format for file: " + filepath);
        return;
      }
      
      try (ClipboardReader reader = format.getReader(new FileInputStream(file))) {
        Clipboard clipboard = reader.read();
        schematicCache.put(filepath, clipboard);
      }
    } catch (IOException e) {
      ProjectKorra.log.severe("[EarthFort] Failed to load schematic: " + filepath);
      ProjectKorra.log.severe(e.getMessage());
    }
  }

  @Override
  public void stop() {
    // Remove all fort markers when the ability is disabled
    for (World world : Bukkit.getWorlds()) {
      for (Entity entity : world.getEntities()) {
        if (entity.getType() == EntityType.INTERACTION && "EARTHFORT".equals(entity.getName())) {
          entity.remove();
        }
      }
    }
    
    // Clear the schematic cache to free memory
    if (schematicCache != null) {
      schematicCache.clear();
      schematicCache = null;
    }
    
    // Clear other static maps
    if (sandMap != null) {
      sandMap.clear();
      sandMap = null;
    }
    
    if (blackstoneMap != null) {
      blackstoneMap.clear();
      blackstoneMap = null;
    }
    
    ProjectKorra.log.info(getName() + " " + getVersion() + " by " + getAuthor() + " disabled!");
  }

  @Override
  public void remove() {
    // Ensure the fort marker is properly removed
    if (fortMarker != null) {
      if (fortMarker.isValid()) {
        fortMarker.remove();
      }
      fortMarker = null;
    }
    super.remove();
  }

  /**
   * Checks if a block should be protected from being replaced by the EarthFort.
   * This includes blocks with inventories and other important blocks.
   * 
   * @param block The block to check
   * @return true if the block should not be replaced, false if it can be replaced
   */
  private boolean isProtectedBlock(Block block) {
    // Check if the block state implements any inventory-related interfaces
    org.bukkit.block.BlockState state = block.getState();
    
    // Check for blocks with inventories
    if (state instanceof org.bukkit.inventory.InventoryHolder) {
      return true;
    }
    
    // Check for blocks with special functionality
    if (state instanceof org.bukkit.block.Jukebox || 
        state instanceof org.bukkit.block.Sign || 
        state instanceof org.bukkit.block.Bed || 
        state instanceof org.bukkit.block.CreatureSpawner ||
        state instanceof org.bukkit.block.CommandBlock) {
      return true;
    }
    
    // Check for specific block types that might not implement interfaces
    Material type = block.getType();
    return type.name().contains("SHULKER_BOX") || // Covers all shulker box colors
           type.name().contains("COMMAND_BLOCK") ||
           type.name().contains("ANVIL") ||
           type == Material.ENCHANTING_TABLE ||
           type == Material.BEACON ||
           type == Material.LODESTONE ||
           type == Material.RESPAWN_ANCHOR ||
           type == Material.DRAGON_EGG;
  }
}
