package com.thelastblockbender.earthmole;

import org.bukkit.Color;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.Particle;
import org.bukkit.Particle.DustOptions;
import org.bukkit.block.Block;
import org.bukkit.block.BlockFace;
import org.bukkit.entity.Player;
import org.bukkit.event.HandlerList;
import org.bukkit.event.Listener;
import org.bukkit.scheduler.BukkitRunnable;
import org.bukkit.util.Vector;

import com.projectkorra.projectkorra.GeneralMethods;
import com.projectkorra.projectkorra.ProjectKorra;
import com.projectkorra.projectkorra.ability.AddonAbility;
import com.projectkorra.projectkorra.ability.EarthAbility;
import com.projectkorra.projectkorra.configuration.ConfigManager;
import com.projectkorra.projectkorra.util.StatisticsMethods;
import com.projectkorra.projectkorra.util.TempBlock;

import net.md_5.bungee.api.ChatColor;

public class EarthMole extends EarthAbility implements AddonAbility {
   private Listener EML;
   private Location playerStartLoc;
   private boolean lastDigFlag;
   private long blockRevertTime;
   private long cooldown;
   private long duration;
   private double speed;
   private int depth;
   private long currentLevel;

   public EarthMole(Player player) {
      super(player);
      if (!this.bPlayer.isOnCooldown(this)) {
         if (this.bPlayer.canBend(this)) {
            if (player.isOnGround()) {
               if (this.checkDepth()) {
                  this.setField();
                  this.modify();
                  this.firstDig();
                  this.start();
               }

            }
         }
      }
   }

   private void modify() {
      int statLevel = StatisticsMethods.getId("AbilityLevel_" + this.getName());
      this.currentLevel = GeneralMethods.limitLevels(this.player, statLevel);
      this.duration = this.currentLevel * 700L + 5000L;
   }

   public void setField() {
      this.playerStartLoc = this.player.getLocation().clone();
      this.blockRevertTime = 1000L;
      this.speed = ConfigManager.getConfig().getDouble("ExtraAbilities.Hiro3.Earth.EarthMole.Speed");
      this.duration = ConfigManager.getConfig().getLong("ExtraAbilities.Hiro3.Earth.EarthMole.Duration");
      this.cooldown = ConfigManager.getConfig().getLong("ExtraAbilities.Hiro3.Earth.EarthMole.Cooldown");
   }

   public void progress() {
      if (GeneralMethods.isRegionProtectedFromBuild(this, this.player.getLocation())) {
         this.bPlayer.addCooldown(this);
         this.remove();
      } else if (this.player.isSneaking() && System.currentTimeMillis() <= this.getStartTime() + this.duration) {
         this.checkOverGround();
         this.checkStuck();
         this.clearWay();
      } else {
         this.lastDig();
         if (this.lastDigFlag) {
            this.bPlayer.addCooldown(this);
         }

         this.remove();
      }
   }

   public void checkOverGround() {
      Location loc = this.player.getLocation().clone();
      boolean flag = true;

      for(int i = -1; i < 2; ++i) {
         for(int j = -1; j < 2; ++j) {
            for(int k = 0; k < 3; ++k) {
               loc.add((double)i, (double)k, (double)j);
               if (!loc.getBlock().isPassable()) {
                  flag = false;
               }

               loc.subtract((double)i, (double)k, (double)j);
            }
         }
      }

      if (flag) {
         this.bPlayer.addCooldown(this);
         this.remove();
      }

   }

   public void checkStuck() {
      Block block = this.player.getEyeLocation().getBlock();
      if (!block.isPassable()) {
         TempBlock tbl;
         if (block.getRelative(BlockFace.UP, 1).getType().equals(Material.SAND)) {
            tbl = new TempBlock(block.getRelative(BlockFace.UP, 1), Material.SANDSTONE);
            tbl.setRevertTime(10000L);
         } else if (block.getRelative(BlockFace.UP, 1).getType().equals(Material.GRAVEL)) {
            tbl = new TempBlock(block.getRelative(BlockFace.UP, 1), Material.STONE);
            tbl.setRevertTime(10000L);
         }

         tbl = new TempBlock(block, Material.AIR);
         tbl.setRevertTime(10000L);
      }

   }

   public void lastDig() {
      int colorNo = 0;
      this.lastDigFlag = true;
      boolean chain = false;
      int airCount = 0;
      int count = 0;
      double launchPower = 0.0D;
      Location loc = this.player.getEyeLocation();
      Block block = loc.getBlock();
      Location particleLoc = this.player.getLocation();
      Location tmpLoc = this.player.getLocation().getBlock().getLocation().add(0.5D, 0.0D, 0.5D);
      tmpLoc.setDirection(this.player.getLocation().getDirection());
      this.player.teleport(tmpLoc);

      for(; airCount < 4 && this.lastDigFlag; ++count) {
         block = block.getRelative(BlockFace.UP, 1);
         if (!chain) {
            airCount = 0;
         }

         if (block.isPassable()) {
            ++airCount;
            chain = true;
         } else {
            if (!GeneralMethods.isSolid(block) || !this.isEarthbendable(block)) {
               this.lastDigFlag = false;
               chain = false;
               this.player.sendMessage(ChatColor.GREEN + "You sense the unbendable blocks on your way up. Cooldown reset!");
               this.remove();
               return;
            }

            if (!block.getType().equals(Material.SAND) && !block.getType().equals(Material.SANDSTONE)) {
               colorNo = 0;
               particleLoc = block.getLocation().add(0.5D, 0.0D, 0.5D);
            } else {
               colorNo = 1;
               particleLoc = block.getLocation().add(0.5D, 0.0D, 0.5D);
            }

            TempBlock tbl = new TempBlock(block, Material.AIR);
            tbl.setRevertTime((long)(50 * 15 * (count / 30 + 1)));
            chain = false;
         }
      }

      launchPower = 0.5D + (double)(count / 300);
      this.player.setVelocity((new Vector(0.0D, Math.sqrt((double)count), 0.0D)).multiply(launchPower));
      this.particles(particleLoc, colorNo);
   }

   public void clearWay() {
      Block block = this.player.getLocation().add(this.player.getLocation().getDirection().setY(0)).getBlock();
      TempBlock tbl;
      if (GeneralMethods.isSolid(block) && this.isEarthbendable(block)) {
         tbl = new TempBlock(block, Material.AIR);
         tbl.setRevertTime(this.blockRevertTime);
      }

      block = block.getRelative(BlockFace.UP, 1);
      if (GeneralMethods.isSolid(block) && this.isEarthbendable(block)) {
         if (block.getRelative(BlockFace.UP, 1).getType().equals(Material.SAND)) {
            tbl = new TempBlock(block.getRelative(BlockFace.UP, 1), Material.SANDSTONE);
            tbl.setRevertTime(this.blockRevertTime);
         } else if (block.getRelative(BlockFace.UP, 1).getType().equals(Material.GRAVEL)) {
            tbl = new TempBlock(block.getRelative(BlockFace.UP, 1), Material.STONE);
            tbl.setRevertTime(this.blockRevertTime);
         }

         tbl = new TempBlock(block, Material.AIR);
         tbl.setRevertTime(this.blockRevertTime);
      }

      if (this.player.isOnGround()) {
         this.player.setVelocity(this.player.getLocation().getDirection().setY(0).multiply(this.speed));
      }

   }

   public void firstDig() {
      Block block = this.playerStartLoc.getBlock().getRelative(BlockFace.DOWN, 1);

      for(int i = 0; i < this.depth; ++i) {
         TempBlock tbl = new TempBlock(block, Material.AIR);
         tbl.setRevertTime(this.blockRevertTime);
         block = block.getRelative(BlockFace.DOWN, 1);
      }

      Location tmpLoc = block.getLocation().add(0.5D, 1.0D, 0.5D);
      tmpLoc.setDirection(this.player.getLocation().getDirection());
      this.player.teleport(tmpLoc);
   }

   public boolean checkDepth() {
      int count = 1;

      for(Block block = this.player.getLocation().getBlock().getRelative(BlockFace.DOWN, 1); count < 5 && (GeneralMethods.isSolid(block) && this.isEarthbendable(block) || block.isPassable()); ++count) {
         block = block.getRelative(BlockFace.DOWN, 1);
      }

      this.depth = count - 1;
      if (count > 2) {
         return true;
      } else {
         this.player.sendMessage(ChatColor.GREEN + "You sense the unbendable blocks on your way down.");
         return false;
      }
   }

   public void particles(Location loc, int c) {
      double r = 0.0D;
      double tmp = 0.0025D;
      double angle = 10.0D;
      double angle2 = Math.toRadians(angle);
      DustOptions dustOptions;
      if (c == 1) {
         dustOptions = new DustOptions(Color.fromRGB(220, 190, 91), 1.0F);
      } else {
         dustOptions = new DustOptions(Color.fromRGB(132, 67, 41), 1.0F);
      }

      while(r >= 0.0D) {
         if (r >= 0.25D) {
            tmp = -tmp;
            r = 0.25D;
         }

         loc.setY(loc.getY() + 0.03D);
         loc.setX(loc.getX() + r * Math.cos(angle2));
         loc.setZ(loc.getZ() + r * Math.sin(angle2));
         this.player.getWorld().spawnParticle(Particle.DUST, loc, 1, dustOptions);
         angle += 10.0D;
         angle2 = Math.toRadians(angle);
         r += tmp;
      }

   }

   public long getCooldown() {
      return this.cooldown;
   }

   public Location getLocation() {
      return null;
   }

   public String getName() {
      return "EarthMole";
   }

   public boolean isHarmlessAbility() {
      return false;
   }

   public boolean isSneakAbility() {
      return true;
   }

   public String getAuthor() {
      return "Hiro3";
   }

   public String getVersion() {
      return "2.1";
   }

   public String getDescription() {
      return "Move like a mole underground as Bumi did!";
   }

   public String getInstructions() {
      return "Hold sneak to burrow under the ground, let go to dig your way back up.";
   }

   public void remove() {
      super.remove();
      (new BukkitRunnable() {
         public void run() {
            EarthMole.this.checkStuck();
         }
      }).runTaskLater(ProjectKorra.plugin, 20L);
   }

   public void load() {
      this.EML = new EarthMoleListener();
      ProjectKorra.log.info("Succesfully enabled " + this.getName() + " by " + this.getAuthor());
      ConfigManager.getConfig().addDefault("ExtraAbilities.Hiro3.Earth.EarthMole.Cooldown", 5000);
      ConfigManager.getConfig().addDefault("ExtraAbilities.Hiro3.Earth.EarthMole.Speed", 0.5D);
      ConfigManager.getConfig().addDefault("ExtraAbilities.Hiro3.Earth.EarthMole.Duration", 10000);
      ConfigManager.defaultConfig.save();
   }

   public void stop() {
      ProjectKorra.log.info("Successfully disabled " + this.getName() + " by " + this.getAuthor());
      HandlerList.unregisterAll(this.EML);
      super.remove();
   }
}
