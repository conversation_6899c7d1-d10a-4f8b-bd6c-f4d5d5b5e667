package com.thelastblockbender.earthmole;

import com.projectkorra.projectkorra.BendingPlayer;
import com.projectkorra.projectkorra.ability.CoreAbility;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.player.PlayerToggleSneakEvent;

public class EarthMoleListener implements Listener {
   @EventHandler
   public void onSneak(PlayerToggleSneakEvent event) {
      Player player = event.getPlayer();
      BendingPlayer bPlayer = BendingPlayer.getBendingPlayer(player);
      if (!event.isCancelled() && bPlayer != null) {
         if (!bPlayer.getBoundAbilityName().equalsIgnoreCase((String)null)) {
            if (!player.isSneaking()) {
               if (bPlayer.getBoundAbilityName().equalsIgnoreCase("EarthMole") && CoreAbility.getAbility(player, EarthMole.class) == null) {
                  new EarthMole(player);
               }

            }
         }
      }
   }
}
