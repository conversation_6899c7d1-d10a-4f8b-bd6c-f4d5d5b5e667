package me.simplicitee.fartbending.fartblast;

import java.util.Iterator;

import org.bukkit.Location;
import org.bukkit.entity.Entity;
import org.bukkit.entity.Player;
import org.bukkit.potion.PotionEffect;
import org.bukkit.potion.PotionEffectType;
import org.bukkit.util.Vector;

import com.projectkorra.projectkorra.GeneralMethods;
import com.projectkorra.projectkorra.ProjectKorra;
import com.projectkorra.projectkorra.TLBMethods;
import com.projectkorra.projectkorra.ability.AddonAbility;
import com.projectkorra.projectkorra.ability.AirAbility;
import com.projectkorra.projectkorra.configuration.ConfigManager;
import com.projectkorra.projectkorra.util.ParticleEffect;
import com.projectkorra.projectkorra.util.StatisticsMethods;

public class FartBlast extends AirAbility implements AddonAbility {
   private long cooldown;
   private long currentLevel;
   private long radius;
   private Long fartPower;
   private int fartPotency;

   public FartBlast(Player player) {
      super(player);
      int statLevel = StatisticsMethods.getId("AbilityLevel_" + this.getName());
      this.currentLevel = TLBMethods.limitLevels(player, statLevel);
      this.radius = TLBMethods.getLong("Abilities.Air.FartBlast.Radius", this.currentLevel);
      this.cooldown = TLBMethods.getLong("Abilities.Air.FartBlast.Cooldown", this.currentLevel);
      this.fartPower = TLBMethods.getLong("Abilities.Air.FartBlast.FartPower", this.currentLevel);
      this.fartPotency = TLBMethods.getInt("Abilities.Air.FartBlast.FartPotency", this.currentLevel);
      this.start();
   }

   public void progress() {
      Location loc = this.getLocation();
      this.playFartParticles(loc, 4, 0.7D);
      Iterator var3 = GeneralMethods.getEntitiesAroundPoint(loc.add(0.0D, -1.0D, 0.0D), (double)this.radius).iterator();

      while(var3.hasNext()) {
         Entity e = (Entity)var3.next();
         if (e instanceof Player && e.getEntityId() != this.player.getEntityId()) {
            ((Player)e).addPotionEffect(new PotionEffect(PotionEffectType.NAUSEA, this.fartPotency, 2));
            System.out.println("ew");
         }
      }

      this.player.setVelocity(this.player.getLocation().getDirection().setY(0.1D).multiply((float)this.fartPower));
      this.bPlayer.addCooldown("Flatulence", this.cooldown);
      this.remove();
   }

   public boolean isSneakAbility() {
      return true;
   }

   public boolean isHarmlessAbility() {
      return false;
   }

   public boolean isIgniteAbility() {
      return false;
   }

   public boolean isExplosiveAbility() {
      return false;
   }

   public long getCooldown() {
      return this.cooldown;
   }

   public String getName() {
      return "Flatulence";
   }

   public Location getLocation() {
      Vector dir = this.player.getLocation().getDirection().multiply(-0.2D).setY(-0.02D);
      return this.player.getLocation().add(0.0D, 0.9D, 0.0D).add(dir);
   }

   public String getDescription() {
      return "Get creative with your air bending and bend the air from a whole new angle!";
   }

   public String getInstructions() {
      return "Sneak to relieve some pressure (may cause nausea)";
   }

   public void playFartParticles(Location loc, int amount, double offsets) {
      // Create expanding cloud effect
      for (double i = 0.2; i <= radius * 2; i += 0.3) {
         // Calculate expanding radius
         double currentRadius = i * 0.5;
         
         // Create cloud at different angles
         for (int angle = 0; angle < 360; angle += 45) {
            double x = currentRadius * Math.cos(Math.toRadians(angle));
            double z = currentRadius * Math.sin(Math.toRadians(angle));
            
            // Create location with offset
            Location particleLoc = loc.clone().add(x, -0.5, z);
            
            // Display green-tinted particles for fart effect
            ParticleEffect.SNEEZE.display(particleLoc, 1, 0.1, 0.1, 0.1);
            
            // Add some colored particles for the green effect
            if (Math.random() > 0.6) {
               ParticleEffect.REDSTONE.display(particleLoc, 1, 0.1, 0.1, 0.1, 0, new org.bukkit.Particle.DustOptions(org.bukkit.Color.fromRGB(100, 200, 0), 1.0F));
            }
         }
      }
      
      // Add some standard airbending particles at the center
      AirAbility.playAirbendingParticles(loc.add(0.0D, -1.0D, 0.0D), amount, offsets, offsets, offsets);
   }

   public void load() {
      ProjectKorra.plugin.getServer().getPluginManager().registerEvents(new FartListener(), ProjectKorra.plugin);
      ConfigManager.getConfig().addDefault("Abilities.Air.FartBlast.Cooldown", 5000);
      ConfigManager.getConfig().addDefault("Abilities.Air.FartBlast.Radius", 0.4D);
      ConfigManager.getConfig().addDefault("Abilities.Air.FartBlast.FartPower", 1.2D);
      ConfigManager.getConfig().addDefault("Abilities.Air.FartBlast.FartPotency", 100);
      ConfigManager.defaultConfig.save();
   }

   public void stop() {
   }

   public String getAuthor() {
      return null;
   }

   public String getVersion() {
      return null;
   }
}
