package com.Pride.korra.FireDrive;

import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.player.PlayerToggleSneakEvent;

import com.projectkorra.projectkorra.ability.CoreAbility;

public class FireDriveListener implements Listener {
   @EventHandler
   public void onSneak(PlayerToggleSneakEvent event) {
      if (!event.isCancelled()) {
         if (!CoreAbility.hasAbility(event.getPlayer(), FireDrive.class)) {
            new FireDrive(event.getPlayer());
         }
      }
   }
}
