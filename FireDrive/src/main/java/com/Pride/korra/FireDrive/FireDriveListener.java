package com.Pride.korra.FireDrive;

import com.projectkorra.projectkorra.ability.CoreAbility;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.player.PlayerToggleSneakEvent;

public class FireDriveListener implements Listener {
   @EventHandler
   public void onSneak(PlayerToggleSneakEvent event) {
      if (!event.isCancelled()) {
         if (!CoreAbility.hasAbility(event.getPlayer(), FireDrive.class)) {
            new FireDrive(event.getPlayer());
         }
      }
   }
}
