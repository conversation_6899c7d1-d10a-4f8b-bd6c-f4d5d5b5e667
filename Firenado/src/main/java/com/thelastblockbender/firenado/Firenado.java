package com.thelastblockbender.firenado;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.Set;
import java.util.concurrent.ThreadLocalRandom;

import org.bukkit.Location;
import org.bukkit.entity.ArmorStand;
import org.bukkit.entity.Entity;
import org.bukkit.entity.LivingEntity;
import org.bukkit.entity.Player;
import org.bukkit.util.Vector;

import com.projectkorra.projectkorra.GeneralMethods;
import com.projectkorra.projectkorra.ProjectKorra;
import com.projectkorra.projectkorra.TLBMethods;
import com.projectkorra.projectkorra.ability.AddonAbility;
import com.projectkorra.projectkorra.ability.ComboAbility;
import com.projectkorra.projectkorra.ability.CoreAbility;
import com.projectkorra.projectkorra.ability.FireAbility;
import com.projectkorra.projectkorra.ability.util.ComboManager.AbilityInformation;
import com.projectkorra.projectkorra.command.Commands;
import com.projectkorra.projectkorra.configuration.ConfigManager;
import com.projectkorra.projectkorra.util.ClickType;
import com.projectkorra.projectkorra.util.DamageHandler;
import com.projectkorra.projectkorra.util.StatisticsMethods;

public class Firenado extends FireAbility implements AddonAbility, ComboAbility {
    private final Set<Entity> hitEntities = new HashSet<>();
    
    // Configuration variables
    private long cooldown;
    private double damage;
    private double speed;
    private double range;
    private double maxRadius;
    private double knockback;
    private double knockup;
    private int fireTicks;
    private int particleCount;
    private double spiralHeight;
    private double spiralSpeed;
    
    // Runtime variables
    private Location origin;
    private Location currentLocation;
    private Vector direction;
    private boolean launched;
    private double currentRadius;
    private double currentHeight;
    private double angle;
    private double distance;
    private long currentLevel;

    public Firenado(Player player) {
        super(player);

        // Check if the ability is on cooldown
        if (bPlayer.isOnCooldown(this)) {
            return;
        }

        // Don't allow the move to start underwater
        if (player.getLocation().getBlock().getType().toString().contains("WATER")) {
            return;
        }

        setFields();
        origin = player.getEyeLocation().clone();
        direction = player.getEyeLocation().getDirection().normalize();
        currentLocation = origin.clone();
        launched = true; // Launch immediately since combo is complete
        currentRadius = 0.5;
        currentHeight = 0.5;
        angle = 0;
        distance = 0;

        start();
    }
    
    private void setFields() {
        int statLevel = StatisticsMethods.getId("AbilityLevel_" + this.getName());
        currentLevel = TLBMethods.limitLevels(player, statLevel);
        
        this.cooldown = TLBMethods.getLong("ExtraAbilities.TLB.Firenado.Cooldown", currentLevel);
        this.damage = TLBMethods.getDouble("ExtraAbilities.TLB.Firenado.Damage", currentLevel);
        this.speed = TLBMethods.getDouble("ExtraAbilities.TLB.Firenado.Speed", currentLevel);
        this.range = TLBMethods.getDouble("ExtraAbilities.TLB.Firenado.Range", currentLevel);
        this.maxRadius = TLBMethods.getDouble("ExtraAbilities.TLB.Firenado.MaxRadius", currentLevel);
        this.knockback = TLBMethods.getDouble("ExtraAbilities.TLB.Firenado.Knockback", currentLevel);
        this.knockup = TLBMethods.getDouble("ExtraAbilities.TLB.Firenado.Knockup", currentLevel);
        this.fireTicks = TLBMethods.getInt("ExtraAbilities.TLB.Firenado.FireTicks", currentLevel);
        this.particleCount = TLBMethods.getInt("ExtraAbilities.TLB.Firenado.ParticleCount", currentLevel);
        this.spiralHeight = TLBMethods.getDouble("ExtraAbilities.TLB.Firenado.SpiralHeight", currentLevel);
        this.spiralSpeed = TLBMethods.getDouble("ExtraAbilities.TLB.Firenado.SpiralSpeed", currentLevel);
    }

    @Override
    public void progress() {

        if (!bPlayer.canBendIgnoreBindsCooldowns(this)) {
            remove();
            return;
        }

        // Move and render the tornado
        if (distance >= range) {
            bPlayer.addCooldown(this);
            remove();
            return;
        }

        // Don't stop when hitting ground - tornado travels along the ground
        advanceTornado();
        renderTornado();
        handleDamage();

        // Play sound occasionally
        if (ThreadLocalRandom.current().nextInt(5) == 0) {
            FireAbility.playFirebendingSound(currentLocation);
        }
    }

    private void advanceTornado() {
        // Move the tornado forward along the ground
        Vector moveDirection = direction.clone().multiply(speed);
        moveDirection.setY(0); // Keep it on the ground level
        currentLocation.add(moveDirection);
        distance += speed;

        // Keep the tornado at ground level
        currentLocation.setY(currentLocation.getWorld().getHighestBlockYAt(currentLocation) + 0.1);

        // Gradually increase radius and height as it moves away from player
        double progressRatio = Math.min(distance / (range * 0.7), 1.0);
        currentRadius = 0.5 + (maxRadius - 0.5) * progressRatio;
        currentHeight = 0.5 + (spiralHeight - 0.5) * progressRatio;

        // Update angle for spiral rotation
        angle += spiralSpeed;
    }

    private boolean isUnderwater(Location location) {
        return location.getBlock().getType().toString().contains("WATER");
    }

    private void playTornadoParticles(Location location, int count, double offsetX, double offsetY, double offsetZ) {
        if (isUnderwater(location)) {
            // Use bubble particles underwater
            location.getWorld().spawnParticle(org.bukkit.Particle.BUBBLE_COLUMN_UP, location, count, offsetX, offsetY, offsetZ, 0.1);
        } else {
            // Use fire particles above water
            playFirebendingParticles(location, count, offsetX, offsetY, offsetZ);
        }
    }

    private void renderTornado() {
        // Create multiple spiral levels for the tornado effect
        int levels = (int) Math.ceil(currentHeight * 6); // More levels for smoother tornado

        for (int level = 0; level < levels; level++) {
            double levelHeight = (double) level / levels * currentHeight;
            // Create a right-side-up cone - narrower at bottom, wider at top
            double levelRadius = currentRadius * (0.3 + (levelHeight / currentHeight * 0.7));

            // Create multiple spiral arms for more tornado-like appearance
            int spiralArms = 3;
            int particlesPerArm = Math.max(4, (int) (levelRadius * 6));

            for (int arm = 0; arm < spiralArms; arm++) {
                for (int i = 0; i < particlesPerArm; i++) {
                    // Create spiral pattern with multiple arms
                    double armOffset = (arm * 2 * Math.PI / spiralArms);
                    double spiralAngle = angle + (level * 0.8) + armOffset + (i * 2 * Math.PI / particlesPerArm);

                    // Add some variation to the radius for more natural look
                    double radiusVariation = levelRadius * (0.8 + 0.4 * Math.sin(spiralAngle * 2));

                    double x = Math.cos(spiralAngle) * radiusVariation;
                    double z = Math.sin(spiralAngle) * radiusVariation;

                    Location particleLoc = currentLocation.clone().add(x, levelHeight, z);

                    // Add some randomness for more natural look
                    double randomOffset = 0.15;
                    particleLoc.add(
                        (Math.random() - 0.5) * randomOffset,
                        (Math.random() - 0.5) * randomOffset * 0.5, // Less vertical randomness
                        (Math.random() - 0.5) * randomOffset
                    );

                    // Vary particle count based on level (more at top for cone shape)
                    int particleAmount = Math.max(1, (int) (1 + (levelHeight / currentHeight * 2)));
                    playTornadoParticles(particleLoc, particleAmount, 0.05, 0.05, 0.05);
                }
            }

            // Add inner core particles for density
            if (level % 2 == 0) { // Every other level for performance
                double coreRadius = levelRadius * 0.3;
                for (int i = 0; i < 8; i++) {
                    double coreAngle = angle * 1.5 + (i * Math.PI / 4);
                    double x = Math.cos(coreAngle) * coreRadius;
                    double z = Math.sin(coreAngle) * coreRadius;
                    Location coreLoc = currentLocation.clone().add(x, levelHeight, z);
                    playFirebendingParticles(coreLoc, 1, 0.02, 0.02, 0.02);
                }
            }
        }

        // Add intense base effect (smaller since it's the narrow part)
        playFirebendingParticles(currentLocation, particleCount / 2, currentRadius / 6, 0.2, currentRadius / 6);

        // Add some upward-moving particles for the tornado effect at the top
        for (int i = 0; i < 8; i++) {
            double topAngle = angle + (i * 2 * Math.PI / 8);
            double topRadius = currentRadius * 0.9; // Near the wide top
            double x = Math.cos(topAngle) * topRadius;
            double z = Math.sin(topAngle) * topRadius;
            Location topLoc = currentLocation.clone().add(x, currentHeight * 0.9, z);
            playFirebendingParticles(topLoc, 3, 0.1, 0.2, 0.1);
        }
    }

    private void handleDamage() {
        // Check for entities within the tornado's radius and height
        for (Entity entity : GeneralMethods.getEntitiesAroundPoint(currentLocation, currentRadius)) {
            if (entity instanceof LivingEntity && entity.getEntityId() != player.getEntityId() && !(entity instanceof ArmorStand)) {
                if (entity instanceof Player && Commands.invincible.contains(((Player) entity).getName())) {
                    continue;
                }

                // Check if entity is within the tornado's height
                double entityHeight = entity.getLocation().getY() - currentLocation.getY();
                if (entityHeight >= 0 && entityHeight <= currentHeight) {
                    if (!hitEntities.contains(entity)) {
                        hitEntities.add(entity);

                        // Damage the entity
                        DamageHandler.damageEntity(entity, player, damage, this);

                        // Set on fire
                        if (entity.getFireTicks() < fireTicks) {
                            entity.setFireTicks(fireTicks);
                        }

                        // Apply knockback in a spiral pattern
                        Vector knockbackDirection = entity.getLocation().toVector().subtract(currentLocation.toVector()).normalize();
                        knockbackDirection.setY(knockup);

                        // Add some rotational force for tornado effect
                        Vector rotationalForce = new Vector(-knockbackDirection.getZ(), 0, knockbackDirection.getX()).multiply(0.3);
                        knockbackDirection.add(rotationalForce);

                        entity.setVelocity(knockbackDirection.multiply(knockback));
                    }
                }
            }
        }
    }

    // Required ComboAbility methods
    @Override
    public ArrayList<AbilityInformation> getCombination() {
        ArrayList<AbilityInformation> combo = new ArrayList<>();
        combo.add(new AbilityInformation("FireBlast", ClickType.SHIFT_DOWN));
        combo.add(new AbilityInformation("FireBlast", ClickType.SHIFT_UP));
        combo.add(new AbilityInformation("FireBlast", ClickType.SHIFT_DOWN));
        combo.add(new AbilityInformation("HeatControl", ClickType.SHIFT_UP));
        return combo;
    }

    @Override
    public Object createNewComboInstance(Player player) {
        return new Firenado(player);
    }

    @Override
    public String getInstructions() {
        return "Fireblast (tap sneak) > Fireblast (hold sneak) > HeatControl (unsneak)";
    }

    // Required Ability methods
    @Override
    public long getCooldown() {
        return cooldown;
    }

    @Override
    public Location getLocation() {
        return currentLocation != null ? currentLocation : origin;
    }

    @Override
    public String getName() {
        return "Firenado";
    }

    @Override
    public boolean isHarmlessAbility() {
        return false;
    }

    @Override
    public boolean isSneakAbility() {
        return true;
    }

    @Override
    public boolean isHiddenAbility() {
        return false;
    }

    @Override
    public String getDescription() {
        return "Create a spiraling tornado of fire that grows larger and more powerful as it travels away from you. " +
               "The firenado damages and burns enemies while pulling them into its vortex.";
    }

    @Override
    public String getAuthor() {
        return "TLB";
    }

    @Override
    public String getVersion() {
        return "1.0";
    }

    @Override
    public void load() {

        // Add default configuration values
        ConfigManager.getConfig().addDefault("ExtraAbilities.TLB.Firenado.Enabled", true);
        ConfigManager.getConfig().addDefault("ExtraAbilities.TLB.Firenado.Cooldown", 8000);
        ConfigManager.getConfig().addDefault("ExtraAbilities.TLB.Firenado.Damage", 3.0);
        ConfigManager.getConfig().addDefault("ExtraAbilities.TLB.Firenado.Speed", 0.8);
        ConfigManager.getConfig().addDefault("ExtraAbilities.TLB.Firenado.Range", 20.0);
        ConfigManager.getConfig().addDefault("ExtraAbilities.TLB.Firenado.MaxRadius", 4.0);
        ConfigManager.getConfig().addDefault("ExtraAbilities.TLB.Firenado.Knockback", 1.5);
        ConfigManager.getConfig().addDefault("ExtraAbilities.TLB.Firenado.Knockup", 0.8);
        ConfigManager.getConfig().addDefault("ExtraAbilities.TLB.Firenado.FireTicks", 60);
        ConfigManager.getConfig().addDefault("ExtraAbilities.TLB.Firenado.ParticleCount", 8);
        ConfigManager.getConfig().addDefault("ExtraAbilities.TLB.Firenado.SpiralHeight", 6.0);
        ConfigManager.getConfig().addDefault("ExtraAbilities.TLB.Firenado.SpiralSpeed", 0.3);

        ConfigManager.defaultConfig.save();
        ProjectKorra.getCollisionInitializer().addComboAbility((CoreAbility) this);
        ProjectKorra.log.info("Enabled " + this.getName() + " by " + this.getAuthor());
    }

    @Override
    public void stop() {
        ProjectKorra.log.info("Disabled " + this.getName() + " by " + this.getAuthor());
    }
}
