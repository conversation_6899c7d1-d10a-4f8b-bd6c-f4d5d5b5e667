package com.thelastblockbender.firenado;

import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.player.PlayerAnimationEvent;
import org.bukkit.event.player.PlayerAnimationType;

import com.projectkorra.projectkorra.BendingPlayer;
import com.projectkorra.projectkorra.ability.CoreAbility;

public class FirenadoListener implements Listener {
    
    @EventHandler
    public void onPlayerAnimation(PlayerAnimationEvent event) {
        if (event.getAnimationType() != PlayerAnimationType.ARM_SWING) {
            return;
        }
        
        Player player = event.getPlayer();
        BendingPlayer bPlayer = BendingPlayer.getBendingPlayer(player);
        
        if (bPlayer == null) {
            return;
        }
        
        // Check if player has Firenado ability active and is holding FireBlast
        Firenado firenado = CoreAbility.getAbility(player, Firenado.class);
        if (firenado != null && bPlayer.getBoundAbilityName().equalsIgnoreCase("FireBlast")) {
            // Launch the firenado
            firenado.launch();
        }
    }
}
