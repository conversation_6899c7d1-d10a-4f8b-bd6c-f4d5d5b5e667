package com.chrismwiggs.sweepkick;

import java.util.ArrayList;
import java.util.Collection;

import com.projectkorra.projectkorra.util.ClickType;
import com.projectkorra.projectkorra.ability.util.ComboManager;
import com.projectkorra.projectkorra.ability.CoreAbility;
import com.projectkorra.projectkorra.GeneralMethods;
import com.projectkorra.projectkorra.ProjectKorra;
import com.projectkorra.projectkorra.util.DamageHandler;
import com.projectkorra.projectkorra.util.ParticleEffect;
import com.projectkorra.projectkorra.ability.Ability;
import com.projectkorra.projectkorra.configuration.ConfigManager;
import com.projectkorra.projectkorra.TLBMethods;
import com.projectkorra.projectkorra.util.StatisticsMethods;
import com.projectkorra.projectkorra.util.TempBlock;
import com.projectkorra.projectkorra.ability.ComboAbility;
import com.projectkorra.projectkorra.ability.AddonAbility;
import com.projectkorra.projectkorra.ability.ChiAbility;

import org.bukkit.util.Vector;
import org.bukkit.Location;
import org.bukkit.block.Block;
import org.bukkit.Material;
import org.bukkit.Sound;
import org.bukkit.entity.LivingEntity;
import org.bukkit.entity.Entity;
import org.bukkit.entity.Player;

public class SweepKick extends ChiAbility implements AddonAbility, ComboAbility
{
    private static final long DEFAULT_COOLDOWN = 12000L;
    private static final long DEFAULT_STUN_DURATION = 1000L;
    private static final long DEFAULT_SWEEP_DURATION = 500L;
    private static final double DEFAULT_SWEEP_RADIUS = 3.0;
    private static final int UpdateCount = 12;
    private static int knockback;
    private static boolean enabled;
    private static long cooldown;
    private static long stunDuration;
    private static long sweepDuration;
    private static double radius;
    private static long updateDelay;
    private static long damage;
    private double theta;
    private double yaw;
    private long lastUpdate;
    private long currentLevel;
    
    static {
        SweepKick.enabled = true;
        SweepKick.cooldown = 12000L;
        SweepKick.stunDuration = 1000L;
        SweepKick.sweepDuration = 500L;
        SweepKick.radius = 3.0;
        SweepKick.updateDelay = SweepKick.sweepDuration / 12L;
    }
    
    public SweepKick(final Player player) {
        super(player);
        this.theta = 0.0;
        this.lastUpdate = 0L;
        this.yaw = Math.toRadians(player.getLocation().getYaw());
        final int statLevel = StatisticsMethods.getId("AbilityLevel_" + this.getName());
        this.currentLevel = TLBMethods.limitLevels(player, statLevel);
        SweepKick.enabled = ConfigManager.getConfig().getBoolean("Abilities.Chi.SweepKick.Enabled");
        SweepKick.cooldown = TLBMethods.getLong("Abilities.Chi.SweepKick.Cooldown", this.currentLevel);
        SweepKick.stunDuration = TLBMethods.getLong("Abilities.Chi.SweepKick.StunDuration", this.currentLevel);
        SweepKick.sweepDuration = TLBMethods.getLong("Abilities.Chi.SweepKick.SweepDuration", this.currentLevel);
        SweepKick.radius = TLBMethods.getDouble("Abilities.Chi.SweepKick.SweepRadius", this.currentLevel);
        SweepKick.knockback = TLBMethods.getInt("Abilities.Chi.SweepKick.Knockback", this.currentLevel);
        SweepKick.damage = TLBMethods.getInt("Abilities.Chi.SweepKick.Damage", this.currentLevel);
        SweepKick.updateDelay = SweepKick.sweepDuration / 12L;
        this.start();
    }
    
    public void progress() {
        final long time = System.currentTimeMillis();
        if (this.player.isDead() || !this.player.isOnline()) {
            this.bPlayer.addCooldown("SweepKick", SweepKick.cooldown);
            this.remove();
            return;
        }
        if (this.bPlayer.isOnCooldown((Ability)this)) {
            this.remove();
            return;
        }
        if (time >= this.lastUpdate + SweepKick.updateDelay) {
            double angle = this.theta + this.yaw;
            final double phi = Math.sin(angle);
            angle = this.normalizeAngle(angle);

            for (final Location l : GeneralMethods.getCircle(this.player.getLocation(), 3, 4, false, true, 0)) {
                Block block = l.getBlock();
                if (block.getType() != Material.ICE) continue;
                ParticleEffect.BLOCK_CRACK.display(l, 5, 0, 0, 0, 0, Material.PACKED_ICE.createBlockData());
                l.getWorld().playSound(l, Sound.BLOCK_GLASS_BREAK, 2f, 5f);
                if (TempBlock.isTempBlock(block)) {
                    TempBlock temp = TempBlock.get(block);
                    if (temp.getState().getType() == Material.WATER) {
                        temp.revertBlock();
                        continue;
                    }
                }
                block.setType(Material.AIR);
            }

            for (double range = 0.0; range < 1.0; range += 0.3) {
                final Location current = this.getAngleLocation(angle, SweepKick.radius * range).add(0.0, (phi + 1.0) / 8.0, 0.0);
                ParticleEffect.CLOUD.display(current, 3, 0.0, 0.0, 0.0, 0.0);
            }
            final Collection<Entity> entities = (Collection<Entity>)this.player.getWorld().getNearbyEntities(this.player.getLocation(), SweepKick.radius, 1.5, SweepKick.radius);
            for (final Entity entity : entities) {
                if (entity == this.player) {
                    continue;
                }
                if (!(entity instanceof LivingEntity)) {
                    continue;
                }
                final Vector entityPos = entity.getLocation().toVector().setY(0);
                final Vector playerPos = this.player.getLocation().toVector().setY(0);
                if (entityPos.distanceSquared(playerPos) > SweepKick.radius * SweepKick.radius) {
                    continue;
                }
                final Vector toEntity = entityPos.subtract(playerPos);
                double entityAngle = Math.atan2(toEntity.getZ(), toEntity.getX());
                entityAngle = this.normalizeAngle(entityAngle);
                if (entityAngle < angle || entityAngle > angle + 0.5235987755982988) {
                    continue;
                }

                DamageHandler.damageEntity(entity, (double)SweepKick.damage, (Ability)this);
                entity.setVelocity(this.player.getLocation().getDirection().multiply(SweepKick.knockback));
            }
            this.theta += 0.5235987755982988;
            this.lastUpdate = time;
        }
        if (this.theta >= 6.283185307179586) {
            this.bPlayer.addCooldown("SweepKick", SweepKick.cooldown);
            this.remove();
        }
    }
    
    private double normalizeAngle(double angle) {
        while (angle < 0.0) {
            angle += 6.283185307179586;
        }
        while (angle > 6.283185307179586) {
            angle -= 6.283185307179586;
        }
        return angle;
    }
    
    private Location getAngleLocation(final double angle, final double radius) {
        final Vector offset = new Vector(Math.cos(angle), 0.0, Math.sin(angle)).multiply(radius);
        return this.player.getLocation().clone().add(offset);
    }
    
    public boolean isEnabled() {
        return SweepKick.enabled;
    }
    
    public boolean isSneakAbility() {
        return false;
    }
    
    public boolean isHarmlessAbility() {
        return false;
    }
    
    public long getCooldown() {
        return SweepKick.cooldown;
    }
    
    public String getName() {
        return "SweepKick";
    }
    
    public Location getLocation() {
        return null;
    }
    
    public void load() {
        ConfigManager.getConfig().addDefault("Abilities.Chi.SweepKick.Enabled", (Object)true);
        ConfigManager.getConfig().addDefault("Abilities.Chi.SweepKick.Cooldown", (Object)10000);
        ConfigManager.getConfig().addDefault("Abilities.Chi.SweepKick.StunDuration", (Object)1000);
        ConfigManager.getConfig().addDefault("Abilities.Chi.SweepKick.SweepDuration", (Object)500);
        ConfigManager.getConfig().addDefault("Abilities.Chi.SweepKick.SweepRadius", (Object)3.0);
        ConfigManager.getConfig().addDefault("Abilities.Chi.SweepKick.Knockback", (Object)1.0);
        ConfigManager.getConfig().addDefault("Abilities.Chi.SweepKick.Damage", (Object)1.5);
        ProjectKorra.getCollisionInitializer().addComboAbility((CoreAbility)this);
        ProjectKorra.log.info("Enabled " + this.getName() + " by " + this.getAuthor());
    }
    
    public void stop() {
        ProjectKorra.log.info("Disabled " + this.getName() + " by " + this.getAuthor());
    }
    
    public String getAuthor() {
        return "";
    }
    
    public String getVersion() {
        return "";
    }
    
    public String getInstructions() {
        return " SwiftKick (Left Click 2x) > Roll (Left Click) >";
    }
    
    public String getDescription() {
        return "Crouch and make a wide kick along the ground, sending your opponent flying backwards!";
    }
    
    public Object createNewComboInstance(final Player player) {
        return new SweepKick(player);
    }
    
    public ArrayList<ComboManager.AbilityInformation> getCombination() {
        final ArrayList<ComboManager.AbilityInformation> combo = new ArrayList<ComboManager.AbilityInformation>();
        combo.add(new ComboManager.AbilityInformation("SwiftKick", ClickType.LEFT_CLICK));
        combo.add(new ComboManager.AbilityInformation("SwiftKick", ClickType.LEFT_CLICK));
        combo.add(new ComboManager.AbilityInformation("Roll", ClickType.LEFT_CLICK));
        return combo;
    }
}
