package com.Pride.korra.VineManipulation;

import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.player.PlayerAnimationType;
import org.bukkit.event.player.PlayerInteractEvent;
import org.bukkit.event.player.PlayerToggleSneakEvent;

import com.projectkorra.projectkorra.BendingPlayer;
import com.projectkorra.projectkorra.ability.CoreAbility;

import io.papermc.paper.event.player.PlayerArmSwingEvent;

public class VineManipListener implements Listener {
   @EventHandler
   public void onSneak(PlayerToggleSneakEvent event) {
      if (!event.isCancelled()) {
         if (event.isSneaking()) {
            BendingPlayer bPlayer = BendingPlayer.getBendingPlayer(event.getPlayer());
            if (bPlayer != null && bPlayer.canBend(CoreAbility.getAbility("VineManipulation")) && CoreAbility.getAbility(event.getPlayer(), VineManipulation.class) == null) {
              
               System.out.println("listener start");
               new VineManipulation(event.getPlayer());
            }

         }
      }
   }

   @EventHandler
   public void onHit(PlayerArmSwingEvent event) {

      System.out.println("listener interacing");
      if (!event.isCancelled() && event.getAnimationType() == PlayerAnimationType.ARM_SWING) {
         Player player = event.getPlayer();
         BendingPlayer bPlayer = BendingPlayer.getBendingPlayer(player);
         VineManipulation vineManip = (VineManipulation)CoreAbility.getAbility(player, VineManipulation.class);

         System.out.println("listener leftclick");
         if (bPlayer != null && CoreAbility.getAbility(player, VineManipulation.class) != null) {
            VineManipulation VineManipulation = CoreAbility.getAbility(player, VineManipulation.class);
         
            if (VineManipulation != null) {
               System.out.println("listener launch");
               VineManipulation.prepareVine();
            }
            
         }

      }
   }

   @EventHandler
   public void onHit(PlayerInteractEvent event) {

      System.out.println("listener interacing");
      if (!event.isCancelled()) {
         Player player = event.getPlayer();
         BendingPlayer bPlayer = BendingPlayer.getBendingPlayer(player);
         VineManipulation vineManip = (VineManipulation)CoreAbility.getAbility(player, VineManipulation.class);

         System.out.println("listener right click");
         if (event.getAction().isRightClick() && bPlayer != null && CoreAbility.getAbility(player, VineManipulation.class) != null) {
            System.out.println("listener armoring");
            new PlantArmor(player);
         }

      }
   }
}
