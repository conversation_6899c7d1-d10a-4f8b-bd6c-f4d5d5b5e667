package com.Pride.korra.VineManipulation;

import java.util.ArrayList;
import java.util.Iterator;

import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.Particle;
import org.bukkit.Sound;
import org.bukkit.block.Block;
import org.bukkit.entity.ArmorStand;
import org.bukkit.entity.Entity;
import org.bukkit.entity.LivingEntity;
import org.bukkit.entity.Player;
import org.bukkit.util.Vector;

import com.projectkorra.projectkorra.GeneralMethods;
import com.projectkorra.projectkorra.ProjectKorra;
import com.projectkorra.projectkorra.TLBMethods;
import com.projectkorra.projectkorra.ability.AddonAbility;
import com.projectkorra.projectkorra.ability.CoreAbility;
import com.projectkorra.projectkorra.ability.PlantAbility;
import com.projectkorra.projectkorra.ability.util.Collision;
import com.projectkorra.projectkorra.airbending.AirShield;
import com.projectkorra.projectkorra.airbending.AirSpout;
import com.projectkorra.projectkorra.airbending.AirSwipe;
import com.projectkorra.projectkorra.configuration.ConfigManager;
import com.projectkorra.projectkorra.earthbending.EarthBlast;
import com.projectkorra.projectkorra.earthbending.EarthSmash;
import com.projectkorra.projectkorra.earthbending.Shockwave;
import com.projectkorra.projectkorra.firebending.FireBlast;
import com.projectkorra.projectkorra.firebending.FireBlastCharged;
import com.projectkorra.projectkorra.firebending.FireManipulation;
import com.projectkorra.projectkorra.firebending.FireShield;
import com.projectkorra.projectkorra.firebending.combustion.Combustion;
import com.projectkorra.projectkorra.util.BlockSource;
import com.projectkorra.projectkorra.util.ClickType;
import com.projectkorra.projectkorra.util.DamageHandler;
import com.projectkorra.projectkorra.util.StatisticsMethods;
import com.projectkorra.projectkorra.util.TempBlock;
import com.projectkorra.projectkorra.waterbending.WaterManipulation;
import com.projectkorra.projectkorra.waterbending.WaterSpout;
import com.projectkorra.projectkorra.waterbending.plant.PlantRegrowth;

public class VineManipulation extends PlantAbility implements AddonAbility {
   private static String path = "ExtraAbilities.Prride.VineManipulation.VineAttack.";
   private long cooldown;
   private Block sourceBlock;
   private boolean selected;
   private TempBlock vine;
   private TempBlock vineGrabTemp;
   private TempBlock temp;
   private Location location;
   private double selectRange;
   private double range;
   private double travelled;
   private Vector direction;
   private long revertTime;
   private double damage;
   private Location loc;
   public boolean readyToLaunch;
   public ArrayList<Player> update = new ArrayList();
   private double collisionRadius;
   private long currentLevel;

   public VineManipulation(Player player) {
      super(player);
      if (this.bPlayer.canBend(this)) {
         this.cooldown = ConfigManager.getConfig().getLong(path + "Cooldown");
         this.selectRange = ConfigManager.getConfig().getDouble(path + "SelectRange");
         this.range = ConfigManager.getConfig().getDouble(path + "Range");
         this.revertTime = ConfigManager.getConfig().getLong(path + "RevertTime");
         this.damage = ConfigManager.getConfig().getDouble(path + "Damage");
         this.collisionRadius = ConfigManager.getConfig().getDouble(path + "CollisionRadius");
         this.sourceBlock = BlockSource.getWaterSourceBlock(player, this.selectRange, ClickType.SHIFT_DOWN, false, false, true);
         if (this.sourceBlock != null && !GeneralMethods.isRegionProtectedFromBuild(this, this.sourceBlock.getLocation())) {
            this.update.add(player);
            this.loc = this.sourceBlock.getLocation();
            this.selected = true;
            this.modify();
            this.start();
         }

      }
   }

   private void modify() {
      int statLevel = StatisticsMethods.getId("AbilityLevel_" + this.getName());
      this.currentLevel = TLBMethods.limitLevels(this.player, statLevel);
      this.cooldown = 10000L - this.currentLevel * 300L;
      this.range = (double)(this.currentLevel + 12L);
   }

   public long getCooldown() {
      return this.cooldown;
   }

   public Location getLocation() {
      return this.location;
   }

   public String getName() {
      return "VineManipulation";
   }

   public boolean isHarmlessAbility() {
      return false;
   }

   public boolean isSneakAbility() {
      return true;
   }

   public void progress() {
      if (this.bPlayer.canBend(getAbility("VineManipulation")) && !this.bPlayer.isOnCooldown(this)) {
         if (this.selected) {
            if (this.player.isSneaking()) {
               if (isPlant(this.sourceBlock)) {
                  new PlantRegrowth(this.player, this.sourceBlock);
                  this.sourceBlock.setType(Material.AIR);
               }

               //if (this.update.contains(this.player)) {
               //   this.location = this.player.getEyeLocation();
               //} else {

               this.location = this.player.getEyeLocation();
                  playPlantbendingSound(this.location);
                  this.location.getWorld().playSound(this.location, Sound.BLOCK_GRASS_BREAK, 1.0F, 1.0F);
                  Particle.BLOCK_MARKER.builder().location(this.location).count(1).offset(0.1, 0.1, 0.1).extra(0).data(Material.SEAGRASS.createBlockData()).force(true).spawn();
            
                  //this.vine = new TempBlock(this.location.getBlock(), Material.TALL_GRASS);
                  //this.vine.setRevertTime(this.revertTime);
                  //this.location = this.location.add(this.direction.clone().multiply(1));
                 // ++this.travelled;
                  //if (this.travelled >= this.range) {
                  //   this.bPlayer.addCooldown(this);
                  //   this.remove();
                   //  return;
                  //}

                  blockAbilities();
              // }

               Vector grabDir = GeneralMethods.getDirection(this.loc, this.player.getEyeLocation().add(0.0D, 1.0D, 0.0D));
               grabDir.normalize().multiply(1);
               this.loc.add(grabDir);
               //this.vineGrabTemp = new TempBlock(this.loc.getBlock(), Material.TALL_GRASS);
               //this.vineGrabTemp.setRevertTime(this.revertTime);
               Particle.BLOCK_MARKER.builder().location(this.location).count(1).offset(0.1, 0.1, 0.1).extra(0).data(Material.SEAGRASS.createBlockData()).force(true).spawn();
            
               
               

               launchVine();
            } 
            else {
               System.out.println("unshifted, ending");
               this.bPlayer.addCooldown(this);
               this.remove();
               return;
            }
         }

      } else {
         this.remove();
      }
   }

   public void prepareVine() {
      this.readyToLaunch = true;
   }

   public void launchVine() {

      System.out.println("Launching");
      if (!this.readyToLaunch) {
         return;
      }

      this.update.remove(this.player);
      blockAbilities();


      if (this.bPlayer.canBend(getAbility("VineManipulation")) && !this.bPlayer.isOnCooldown(this)) {

        System.out.println("Launching YES");
         for(int i = 0; (double)i < 0.03D; ++i) {
            ++this.travelled;
            if (this.travelled >= this.range) {
               this.bPlayer.addCooldown(this);
               this.remove();
               return;
            }

            this.direction = this.player.getEyeLocation().getDirection().normalize();
            this.location = this.location.add(this.direction.clone().multiply(1));
            if (GeneralMethods.isSolid(this.location.getBlock())) {

        System.out.println("Launching solid bad");
               this.bPlayer.addCooldown(this);
               this.remove();
               return;
            }

            if (GeneralMethods.isRegionProtectedFromBuild(this.player, "VineManipulation", this.location)) {
               System.out.println("Launching cant build");
               this.remove();
               return;
            }

            playPlantbendingSound(this.location);
            this.location.getWorld().playSound(this.location, Sound.BLOCK_GRASS_BREAK, 1.0F, 1.0F);
            //this.vine = new TempBlock(this.location.getBlock(), Material.TALL_GRASS);
            //this.vine.setRevertTime(this.revertTime);
            Particle.BLOCK_MARKER.builder().location(this.location).count(1).offset(0.1, 0.1, 0.1).extra(0).data(Material.SEAGRASS.createBlockData()).force(true).spawn();
            
        System.out.println(this.location);      
            Iterator var3 = GeneralMethods.getEntitiesAroundPoint(this.location, this.collisionRadius).iterator();

            while(var3.hasNext()) {

               System.out.println("Launching collision");
               Entity entity = (Entity)var3.next();
               if (entity instanceof LivingEntity && entity.getEntityId() != this.player.getEntityId() && !(entity instanceof ArmorStand)) {
                  DamageHandler.damageEntity(entity, this.damage, this);
                  this.bPlayer.addCooldown(this);
                  this.remove();
                  return;
               }
            }
         }

      } else {
         this.remove();
      }
   }

   private static void blockAbilities() {
      CoreAbility mainAbil = CoreAbility.getAbility(VineManipulation.class);
      CoreAbility fireBlast = CoreAbility.getAbility(FireBlast.class);
      CoreAbility earthBlast = CoreAbility.getAbility(EarthBlast.class);
      CoreAbility waterManip = CoreAbility.getAbility(WaterManipulation.class);
      CoreAbility airSwipe = CoreAbility.getAbility(AirSwipe.class);
      CoreAbility fireBlastCharged = CoreAbility.getAbility(FireBlastCharged.class);
      CoreAbility combustion = CoreAbility.getAbility(Combustion.class);
      CoreAbility earthSmash = CoreAbility.getAbility(EarthSmash.class);
      CoreAbility shockwave = CoreAbility.getAbility(Shockwave.class);
      CoreAbility airSpout = CoreAbility.getAbility(AirSpout.class);
      CoreAbility waterSpout = CoreAbility.getAbility(WaterSpout.class);
      CoreAbility fireShield = CoreAbility.getAbility(FireShield.class);
      CoreAbility fireManip = CoreAbility.getAbility(FireManipulation.class);
      CoreAbility airShield = CoreAbility.getAbility(AirShield.class);
      CoreAbility[] smallAbilities = new CoreAbility[]{airSwipe, earthBlast, waterManip, fireBlast};
      CoreAbility[] largeAbilities = new CoreAbility[]{fireBlastCharged, combustion, earthSmash, shockwave};
      CoreAbility[] spoutAbilities = new CoreAbility[]{airSpout, waterSpout};
      CoreAbility[] shieldAbilities = new CoreAbility[]{fireShield, fireManip, airShield};
      CoreAbility[] var21 = smallAbilities;
      int var20 = smallAbilities.length;

      CoreAbility shieldAbil;
      int var19;
      for(var19 = 0; var19 < var20; ++var19) {
         shieldAbil = var21[var19];
         ProjectKorra.getCollisionManager().addCollision(new Collision(mainAbil, shieldAbil, true, true));
      }

      var21 = largeAbilities;
      var20 = largeAbilities.length;

      for(var19 = 0; var19 < var20; ++var19) {
         shieldAbil = var21[var19];
         ProjectKorra.getCollisionManager().addCollision(new Collision(mainAbil, shieldAbil, true, false));
      }

      var21 = spoutAbilities;
      var20 = spoutAbilities.length;

      for(var19 = 0; var19 < var20; ++var19) {
         shieldAbil = var21[var19];
         ProjectKorra.getCollisionManager().addCollision(new Collision(mainAbil, shieldAbil, false, true));
      }

      var21 = shieldAbilities;
      var20 = shieldAbilities.length;

      for(var19 = 0; var19 < var20; ++var19) {
         shieldAbil = var21[var19];
         ProjectKorra.getCollisionManager().addCollision(new Collision(mainAbil, shieldAbil, true, false));
      }

   }

   public double getCollisionRadius() {
      return this.collisionRadius;
   }

   public void handleCollision(Collision collision) {
      super.handleCollision(collision);
      if (collision.isRemovingFirst()) {
         this.bPlayer.addCooldown(this);
         this.remove();
      }

   }

   public String getAuthor() {
      return "Prride";
   }

   public String getVersion() {
      return "VineManipulation Build V1.2";
   }

   public String getDescription() {
      return "Plantbenders are able to use their variety of plantbending to attack or trap people and mobs. \nSubsequently, many plantbenders are able to ensnare players and mobs within the ground using the roots burrowed beneath. \nPlantbenders can also use the plant life available around them in the environment to their advantage and launch vine attacks.\nPlantbenders are also able to use plants to wrap around and use as armor. Though frail, it provides protection against some attacks.";
   }

   public String getInstructions() {
      return "Left click to root players into the ground. Sneak on plants to manipulate them and release sneak to launch them. When plants are pulled and wrapped, left click to form armor.";
   }

   public void load() {
      ProjectKorra.plugin.getServer().getPluginManager().registerEvents(new VineManipListener(), ProjectKorra.plugin);
      ProjectKorra.log.info(this.getName() + " " + this.getVersion() + " by " + this.getAuthor() + " loaded! ");
      ConfigManager.getConfig().addDefault(path + "Cooldown", 8000);
      ConfigManager.getConfig().addDefault(path + "SelectRange", 12);
      ConfigManager.getConfig().addDefault(path + "Range", 40);
      ConfigManager.getConfig().addDefault(path + "Damage", 4);
      ConfigManager.getConfig().addDefault(path + "RevertTime", 2500);
      ConfigManager.getConfig().addDefault(path + "CollisionRadius", 2);
      ConfigManager.defaultConfig.save();
   }

   public void stop() {
      ProjectKorra.log.info(this.getName() + " " + this.getVersion() + " by " + this.getAuthor() + " stopped! ");
      super.remove();
   }
}
